@echo off
echo ========================================
echo BALLERY.IN Local Development Setup
echo ========================================
echo.

REM Check if XAMPP is installed
if exist "C:\xampp\xampp-control.exe" (
    echo [INFO] XAMPP found at C:\xampp
    goto :start_xampp
) else (
    echo [WARNING] XAMPP not found at C:\xampp
    echo.
    echo Please follow these steps:
    echo 1. Download XAMPP from: https://www.apachefriends.org/download.html
    echo 2. Install XAMPP to C:\xampp (default location)
    echo 3. Run this script again
    echo.
    echo Opening XAMPP download page...
    start https://www.apachefriends.org/download.html
    pause
    exit /b 1
)

:start_xampp
echo [INFO] Starting XAMPP services...
echo.

REM Start XAMPP Control Panel
echo Starting XAMPP Control Panel...
start "" "C:\xampp\xampp-control.exe"

echo.
echo [INFO] Please start Apache and MySQL services in XAMPP Control Panel
echo [INFO] Then press any key to continue...
pause

REM Check if Apache is running
echo [INFO] Checking if Apache is running...
netstat -an | findstr :80 >nul
if %errorlevel% == 0 (
    echo [SUCCESS] Apache is running on port 80
) else (
    echo [ERROR] Apache is not running. Please start it in XAMPP Control Panel
    pause
    exit /b 1
)

REM Check if MySQL is running
echo [INFO] Checking if MySQL is running...
netstat -an | findstr :3306 >nul
if %errorlevel% == 0 (
    echo [SUCCESS] MySQL is running on port 3306
) else (
    echo [ERROR] MySQL is not running. Please start it in XAMPP Control Panel
    pause
    exit /b 1
)

echo.
echo [SUCCESS] XAMPP services are running!
echo.

REM Check if WordPress is already set up
if exist "C:\xampp\htdocs\ballery\wp-config.php" (
    echo [INFO] WordPress appears to be already configured
    goto :open_site
) else (
    echo [INFO] Setting up WordPress and BALLERY.IN theme...
    goto :setup_wordpress
)

:setup_wordpress
echo.
echo ========================================
echo WordPress & Theme Setup
echo ========================================
echo.

REM Create ballery directory in htdocs
if not exist "C:\xampp\htdocs\ballery" (
    echo [INFO] Creating ballery directory...
    mkdir "C:\xampp\htdocs\ballery"
)

REM Copy theme files to htdocs
echo [INFO] Copying BALLERY.IN theme files...
xcopy /E /I /Y "%~dp0*" "C:\xampp\htdocs\ballery\"

REM Check if WordPress needs to be downloaded
if not exist "C:\xampp\htdocs\ballery\wp-admin" (
    echo.
    echo [INFO] WordPress not found. Please download WordPress:
    echo 1. Go to: https://wordpress.org/download/
    echo 2. Download WordPress ZIP file
    echo 3. Extract to C:\xampp\htdocs\ballery\
    echo 4. Run this script again
    echo.
    echo Opening WordPress download page...
    start https://wordpress.org/download/
    pause
    exit /b 1
)

echo.
echo [INFO] Opening WordPress setup in browser...
start http://localhost/ballery/wordpress-setup.php

echo.
echo Please complete the WordPress setup, then press any key to continue...
pause

:open_site
echo.
echo ========================================
echo BALLERY.IN Local Site Ready!
echo ========================================
echo.
echo Your local development environment is ready!
echo.
echo Access URLs:
echo - Website: http://localhost/ballery
echo - WordPress Admin: http://localhost/ballery/wp-admin
echo - phpMyAdmin: http://localhost/phpmyadmin
echo.
echo Default WordPress Login:
echo - Username: admin
echo - Password: BalleryAdmin2024!
echo.
echo Opening your site...
start http://localhost/ballery

echo.
echo [SUCCESS] Setup complete! Your BALLERY.IN site is now running locally.
echo.
pause
