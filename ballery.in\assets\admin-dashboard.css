/**
 * BALLERY.IN Admin Dashboard Styles
 * Comprehensive styling for the custom admin panel
 */

/* Global Admin Styles */
.ballery-admin-dashboard,
.ballery-inquiries-page,
.ballery-services-page,
.ballery-portfolio-page,
.ballery-email-settings,
.ballery-content-page,
.ballery-reports-page {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Dashboard Layout */
.ballery-admin-dashboard {
    background: #f1f1f1;
    margin: -20px -20px 0 -22px;
    padding: 20px;
}

.ballery-admin-dashboard h1 {
    background: white;
    padding: 20px;
    margin: 0 0 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Statistics Grid */
.ballery-stats-grid,
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.ballery-stat-card,
.metric-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.ballery-stat-card:hover,
.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-icon,
.metric-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-content h3,
.metric-content h3 {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
    color: #2c5aa0;
}

.stat-content p,
.metric-content p {
    margin: 5px 0;
    font-weight: 600;
    color: #333;
}

.stat-content small,
.metric-content small {
    color: #666;
    font-size: 0.9rem;
}

/* Quick Actions */
.ballery-quick-actions {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-buttons .button {
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.action-buttons .button:hover {
    transform: translateY(-1px);
}

/* Recent Activity */
.ballery-recent-activity {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.activity-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
    border-radius: 4px;
    margin: 0 -10px;
    padding: 15px 10px;
}

.activity-item:last-child {
    border-bottom: none;
}

.item-icon {
    font-size: 1.5rem;
}

.item-content {
    flex: 1;
}

.item-content h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.item-content p {
    margin: 0 0 5px 0;
    font-size: 13px;
    color: #666;
}

.item-content small {
    font-size: 12px;
    color: #999;
}

/* Status Badges */
.item-status,
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending,
.status-unread { 
    background: #fff3cd; 
    color: #856404; 
}

.status-confirmed,
.status-read,
.status-published,
.status-badge.active { 
    background: #d4edda; 
    color: #155724; 
}

.status-completed { 
    background: #cce5ff; 
    color: #004085; 
}

.status-cancelled,
.status-badge.inactive { 
    background: #f8d7da; 
    color: #721c24; 
}

.status-responded { 
    background: #e2e3e5; 
    color: #383d41; 
}

.status-draft,
.featured-badge.no { 
    background: #fff3cd; 
    color: #856404; 
}

.featured-badge.yes { 
    background: #d4edda; 
    color: #155724; 
}

/* Navigation Tabs */
.nav-tab-wrapper {
    margin: 20px 0;
    border-bottom: 1px solid #ccd0d4;
}

.nav-tab {
    background: #f1f1f1;
    border: 1px solid #ccd0d4;
    border-bottom: none;
    color: #555;
    text-decoration: none;
    padding: 8px 12px;
    margin-right: 5px;
    border-radius: 4px 4px 0 0;
    transition: all 0.2s ease;
}

.nav-tab:hover {
    background: #fafafa;
    color: #2c5aa0;
}

.nav-tab-active {
    background: white;
    color: #2c5aa0;
    border-bottom: 1px solid white;
    margin-bottom: -1px;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.tab-header h2 {
    margin: 0;
    color: #2c5aa0;
}

/* Modal Styles */
.ballery-modal {
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 80%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px 8px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: #2c5aa0;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #999;
    transition: color 0.2s ease;
}

.close:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

/* Portfolio Grid */
.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.portfolio-item {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.portfolio-item.featured {
    border: 2px solid #ff6b35;
}

.project-image {
    position: relative;
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    color: #666;
    font-style: italic;
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff6b35;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.project-details {
    padding: 15px;
}

.project-details h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #333;
}

.project-type {
    color: #2c5aa0;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.project-location,
.project-value {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 14px;
}

.project-actions {
    margin-top: 15px;
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

/* Rating Stars */
.rating .star {
    color: #ddd;
    font-size: 16px;
    transition: color 0.2s ease;
}

.rating .star.filled {
    color: #ffc107;
}

/* View All Links */
.view-all-link {
    display: inline-block;
    margin-top: 15px;
    color: #2c5aa0;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.2s ease;
}

.view-all-link:hover {
    color: #1e3f73;
    text-decoration: underline;
}

/* Export Section */
.export-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.export-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.export-buttons .button {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Date Range Filter */
.date-range-filter {
    background: white;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.date-display {
    color: #666;
    font-style: italic;
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.report-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #2c5aa0;
}

.chart-container {
    margin: 20px 0;
    text-align: center;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 4px;
}

/* No Data States */
.no-projects,
.no-data {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;
    margin-top: 20px;
}

.no-projects h2,
.no-data h2 {
    color: #666;
    margin-bottom: 10px;
}

/* FAQ Suggestions */
.faq-suggestions {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.faq-suggestions ul {
    list-style-type: disc;
    margin-left: 20px;
}

.faq-suggestions li {
    margin-bottom: 8px;
    color: #555;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .reports-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .ballery-recent-activity,
    .ballery-stats-grid,
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 2% auto;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .tab-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .date-range-filter {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* Print Styles */
@media print {
    .ballery-modal,
    .action-buttons,
    .project-actions,
    .export-buttons {
        display: none !important;
    }
    
    .ballery-admin-dashboard {
        background: white;
        margin: 0;
        padding: 0;
    }
    
    .ballery-stat-card,
    .metric-card,
    .activity-section,
    .report-section {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
