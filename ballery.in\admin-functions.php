<?php
/**
 * Additional Admin Functions for BALLERY.IN
 * Portfolio, Email Settings, Content Management, and Reports
 * 
 * @package BALLERY.IN
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Portfolio/Gallery Management Page
 */
function ballery_portfolio_page() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'ballery_portfolio';
    
    // Handle form submissions
    if (isset($_POST['action'])) {
        $action = sanitize_text_field($_POST['action']);
        
        switch ($action) {
            case 'add_project':
                $project_title = sanitize_text_field($_POST['project_title']);
                $project_slug = sanitize_title($_POST['project_title']);
                $project_description = sanitize_textarea_field($_POST['project_description']);
                $project_type = sanitize_text_field($_POST['project_type']);
                $location = sanitize_text_field($_POST['location']);
                $completion_date = sanitize_text_field($_POST['completion_date']);
                $project_value = floatval($_POST['project_value']);
                $client_name = sanitize_text_field($_POST['client_name']);
                $services_provided = sanitize_textarea_field($_POST['services_provided']);
                $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                
                // Handle file upload
                $featured_image = '';
                if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                    $upload = wp_handle_upload($_FILES['featured_image'], array('test_form' => false));
                    if (!isset($upload['error'])) {
                        $featured_image = $upload['url'];
                    }
                }
                
                $result = $wpdb->insert(
                    $table_name,
                    [
                        'project_title' => $project_title,
                        'project_slug' => $project_slug,
                        'project_description' => $project_description,
                        'project_type' => $project_type,
                        'location' => $location,
                        'completion_date' => $completion_date,
                        'project_value' => $project_value,
                        'client_name' => $client_name,
                        'featured_image' => $featured_image,
                        'services_provided' => $services_provided,
                        'is_featured' => $is_featured,
                        'is_published' => 1
                    ],
                    ['%s', '%s', '%s', '%s', '%s', '%s', '%f', '%s', '%s', '%s', '%d', '%d']
                );
                
                if ($result) {
                    echo '<div class="notice notice-success"><p>Project added successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to add project. Please try again.</p></div>';
                }
                break;
                
            case 'update_project':
                $project_id = intval($_POST['project_id']);
                $project_title = sanitize_text_field($_POST['project_title']);
                $project_description = sanitize_textarea_field($_POST['project_description']);
                $project_type = sanitize_text_field($_POST['project_type']);
                $location = sanitize_text_field($_POST['location']);
                $completion_date = sanitize_text_field($_POST['completion_date']);
                $project_value = floatval($_POST['project_value']);
                $client_name = sanitize_text_field($_POST['client_name']);
                $services_provided = sanitize_textarea_field($_POST['services_provided']);
                $is_featured = isset($_POST['is_featured']) ? 1 : 0;
                $is_published = isset($_POST['is_published']) ? 1 : 0;
                
                $update_data = [
                    'project_title' => $project_title,
                    'project_description' => $project_description,
                    'project_type' => $project_type,
                    'location' => $location,
                    'completion_date' => $completion_date,
                    'project_value' => $project_value,
                    'client_name' => $client_name,
                    'services_provided' => $services_provided,
                    'is_featured' => $is_featured,
                    'is_published' => $is_published
                ];
                
                // Handle file upload
                if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                    $upload = wp_handle_upload($_FILES['featured_image'], array('test_form' => false));
                    if (!isset($upload['error'])) {
                        $update_data['featured_image'] = $upload['url'];
                    }
                }
                
                $result = $wpdb->update(
                    $table_name,
                    $update_data,
                    ['id' => $project_id],
                    array_fill(0, count($update_data), '%s'),
                    ['%d']
                );
                
                if ($result !== false) {
                    echo '<div class="notice notice-success"><p>Project updated successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to update project. Please try again.</p></div>';
                }
                break;
                
            case 'delete_project':
                $project_id = intval($_POST['project_id']);
                
                $result = $wpdb->delete(
                    $table_name,
                    ['id' => $project_id],
                    ['%d']
                );
                
                if ($result) {
                    echo '<div class="notice notice-success"><p>Project deleted successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to delete project. Please try again.</p></div>';
                }
                break;
        }
    }
    
    // Get projects
    $projects = $wpdb->get_results("SELECT * FROM $table_name ORDER BY is_featured DESC, completion_date DESC");
    
    ?>
    <div class="wrap ballery-portfolio-page">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-format-gallery"></span>
            Portfolio Gallery
        </h1>
        
        <a href="#add-project" class="page-title-action" id="add-project-btn">Add New Project</a>
        
        <!-- Projects Grid -->
        <div class="portfolio-grid">
            <?php foreach ($projects as $project): ?>
            <div class="portfolio-item <?php echo $project->is_featured ? 'featured' : ''; ?>">
                <div class="project-image">
                    <?php if ($project->featured_image): ?>
                        <img src="<?php echo esc_url($project->featured_image); ?>" alt="<?php echo esc_attr($project->project_title); ?>">
                    <?php else: ?>
                        <div class="no-image">No Image</div>
                    <?php endif; ?>
                    <?php if ($project->is_featured): ?>
                        <span class="featured-badge">Featured</span>
                    <?php endif; ?>
                </div>
                
                <div class="project-details">
                    <h3><?php echo esc_html($project->project_title); ?></h3>
                    <p class="project-type"><?php echo esc_html(ucwords(str_replace('-', ' ', $project->project_type))); ?></p>
                    <p class="project-location"><?php echo esc_html($project->location); ?></p>
                    <p class="project-value">₹<?php echo number_format($project->project_value, 2); ?></p>
                    
                    <div class="project-actions">
                        <button class="button button-small edit-project" 
                                data-project='<?php echo esc_attr(json_encode($project)); ?>'>
                            Edit
                        </button>
                        <button class="button button-small delete-project" 
                                data-id="<?php echo $project->id; ?>"
                                data-title="<?php echo esc_attr($project->project_title); ?>">
                            Delete
                        </button>
                        <span class="status-badge <?php echo $project->is_published ? 'published' : 'draft'; ?>">
                            <?php echo $project->is_published ? 'Published' : 'Draft'; ?>
                        </span>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <?php if (empty($projects)): ?>
        <div class="no-projects">
            <h2>No projects found</h2>
            <p>Start building your portfolio by adding your first project.</p>
            <button class="button button-primary" id="add-first-project">Add Your First Project</button>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Add/Edit Project Modal -->
    <div id="project-modal" class="ballery-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Add New Project</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="project-form" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" id="form-action" value="add_project">
                    <input type="hidden" name="project_id" id="project-id">
                    
                    <table class="form-table">
                        <tr>
                            <th><label for="project-title">Project Title</label></th>
                            <td>
                                <input type="text" name="project_title" id="project-title" 
                                       class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="project-type">Project Type</label></th>
                            <td>
                                <select name="project_type" id="project-type" required>
                                    <option value="">Select Type</option>
                                    <option value="residential">Residential</option>
                                    <option value="commercial">Commercial</option>
                                    <option value="industrial">Industrial</option>
                                    <option value="renovation">Renovation</option>
                                    <option value="consultation">Consultation</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="location">Location</label></th>
                            <td>
                                <input type="text" name="location" id="location" 
                                       class="regular-text" placeholder="City, State">
                            </td>
                        </tr>
                        <tr>
                            <th><label for="completion-date">Completion Date</label></th>
                            <td>
                                <input type="date" name="completion_date" id="completion-date">
                            </td>
                        </tr>
                        <tr>
                            <th><label for="project-value">Project Value (₹)</label></th>
                            <td>
                                <input type="number" name="project_value" id="project-value" 
                                       step="0.01" min="0" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th><label for="client-name">Client Name</label></th>
                            <td>
                                <input type="text" name="client_name" id="client-name" 
                                       class="regular-text" placeholder="Optional">
                            </td>
                        </tr>
                        <tr>
                            <th><label for="featured-image">Featured Image</label></th>
                            <td>
                                <input type="file" name="featured_image" id="featured-image" 
                                       accept="image/*">
                                <p class="description">Upload a high-quality image (JPG, PNG, WebP)</p>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="project-description">Description</label></th>
                            <td>
                                <textarea name="project_description" id="project-description" 
                                          rows="4" class="large-text"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="services-provided">Services Provided</label></th>
                            <td>
                                <textarea name="services_provided" id="services-provided" 
                                          rows="3" class="large-text" 
                                          placeholder="List the services provided for this project"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th>Options</th>
                            <td>
                                <label>
                                    <input type="checkbox" name="is_featured" id="is-featured" value="1">
                                    Featured Project
                                </label><br>
                                <label id="published-row" style="display: none;">
                                    <input type="checkbox" name="is_published" id="is-published" value="1">
                                    Published
                                </label>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" class="button-primary" id="submit-btn" value="Add Project">
                        <button type="button" class="button" onclick="jQuery('#project-modal').hide();">Cancel</button>
                    </p>
                </form>
            </div>
        </div>
    </div>
    
    <style>
    .portfolio-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .portfolio-item {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: transform 0.3s ease;
    }
    
    .portfolio-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .portfolio-item.featured {
        border: 2px solid #ff6b35;
    }
    
    .project-image {
        position: relative;
        height: 200px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .project-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .no-image {
        color: #666;
        font-style: italic;
    }
    
    .featured-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #ff6b35;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .project-details {
        padding: 15px;
    }
    
    .project-details h3 {
        margin: 0 0 10px 0;
        font-size: 16px;
    }
    
    .project-type {
        color: #2c5aa0;
        font-weight: 600;
        margin: 0 0 5px 0;
    }
    
    .project-location, .project-value {
        margin: 0 0 5px 0;
        color: #666;
        font-size: 14px;
    }
    
    .project-actions {
        margin-top: 15px;
        display: flex;
        gap: 8px;
        align-items: center;
        flex-wrap: wrap;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-badge.published {
        background: #d4edda;
        color: #155724;
    }
    
    .status-badge.draft {
        background: #fff3cd;
        color: #856404;
    }
    
    .no-projects {
        text-align: center;
        padding: 60px 20px;
        background: white;
        border-radius: 8px;
        margin-top: 20px;
    }
    
    .ballery-modal {
        position: fixed;
        z-index: 100000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
        background-color: #fefefe;
        margin: 2% auto;
        padding: 0;
        border-radius: 8px;
        width: 90%;
        max-width: 800px;
        max-height: 95vh;
        overflow-y: auto;
    }
    
    .modal-header {
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-header h2 {
        margin: 0;
    }
    
    .close {
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .modal-body {
        padding: 20px;
    }
    </style>
    
    <script>
    jQuery(document).ready(function($) {
        // Add project button
        $('#add-project-btn, #add-first-project').on('click', function(e) {
            e.preventDefault();
            $('#modal-title').text('Add New Project');
            $('#form-action').val('add_project');
            $('#submit-btn').val('Add Project');
            $('#project-form')[0].reset();
            $('#project-id').val('');
            $('#published-row').hide();
            $('#project-modal').show();
        });
        
        // Edit project button
        $('.edit-project').on('click', function() {
            var project = $(this).data('project');
            
            $('#modal-title').text('Edit Project');
            $('#form-action').val('update_project');
            $('#submit-btn').val('Update Project');
            $('#project-id').val(project.id);
            $('#project-title').val(project.project_title);
            $('#project-type').val(project.project_type);
            $('#location').val(project.location);
            $('#completion-date').val(project.completion_date);
            $('#project-value').val(project.project_value);
            $('#client-name').val(project.client_name);
            $('#project-description').val(project.project_description);
            $('#services-provided').val(project.services_provided);
            $('#is-featured').prop('checked', project.is_featured == 1);
            $('#is-published').prop('checked', project.is_published == 1);
            $('#published-row').show();
            $('#project-modal').show();
        });
        
        // Delete project button
        $('.delete-project').on('click', function() {
            var id = $(this).data('id');
            var title = $(this).data('title');
            
            if (confirm('Are you sure you want to delete "' + title + '"?')) {
                var form = $('<form method="post">' +
                    '<input type="hidden" name="action" value="delete_project">' +
                    '<input type="hidden" name="project_id" value="' + id + '">' +
                    '</form>');
                $('body').append(form);
                form.submit();
            }
        });
        
        // Close modal
        $('.close, .ballery-modal').on('click', function(e) {
            if (e.target === this) {
                $('#project-modal').hide();
            }
        });
    });
    </script>
    <?php
}

/**
 * Email Settings Page
 */
function ballery_email_settings_page() {
    // Handle form submission
    if (isset($_POST['save_email_settings'])) {
        // SMTP Settings
        update_option('ballery_smtp_enabled', isset($_POST['smtp_enabled']) ? 1 : 0);
        update_option('ballery_smtp_host', sanitize_text_field($_POST['smtp_host']));
        update_option('ballery_smtp_port', intval($_POST['smtp_port']));
        update_option('ballery_smtp_username', sanitize_text_field($_POST['smtp_username']));
        update_option('ballery_smtp_password', sanitize_text_field($_POST['smtp_password']));
        update_option('ballery_smtp_encryption', sanitize_text_field($_POST['smtp_encryption']));

        // Email Settings
        update_option('ballery_from_email', sanitize_email($_POST['from_email']));
        update_option('ballery_from_name', sanitize_text_field($_POST['from_name']));
        update_option('ballery_admin_email', sanitize_email($_POST['admin_email']));
        update_option('ballery_cc_emails', sanitize_textarea_field($_POST['cc_emails']));

        // Notification Settings
        update_option('ballery_notify_new_booking', isset($_POST['notify_new_booking']) ? 1 : 0);
        update_option('ballery_notify_new_inquiry', isset($_POST['notify_new_inquiry']) ? 1 : 0);
        update_option('ballery_auto_reply_enabled', isset($_POST['auto_reply_enabled']) ? 1 : 0);

        echo '<div class="notice notice-success"><p>Email settings saved successfully!</p></div>';
    }

    // Handle test email
    if (isset($_POST['send_test_email'])) {
        $test_email = sanitize_email($_POST['test_email']);
        $subject = 'BALLERY.IN Email Test';
        $message = 'This is a test email from your BALLERY.IN website. If you received this, your email settings are working correctly!';

        if (wp_mail($test_email, $subject, $message)) {
            echo '<div class="notice notice-success"><p>Test email sent successfully to ' . esc_html($test_email) . '!</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>Failed to send test email. Please check your settings.</p></div>';
        }
    }

    // Get current settings
    $smtp_enabled = get_option('ballery_smtp_enabled', 0);
    $smtp_host = get_option('ballery_smtp_host', '');
    $smtp_port = get_option('ballery_smtp_port', 587);
    $smtp_username = get_option('ballery_smtp_username', '');
    $smtp_password = get_option('ballery_smtp_password', '');
    $smtp_encryption = get_option('ballery_smtp_encryption', 'tls');

    $from_email = get_option('ballery_from_email', get_option('admin_email'));
    $from_name = get_option('ballery_from_name', 'BALLERY.IN');
    $admin_email = get_option('ballery_admin_email', get_option('admin_email'));
    $cc_emails = get_option('ballery_cc_emails', '');

    $notify_new_booking = get_option('ballery_notify_new_booking', 1);
    $notify_new_inquiry = get_option('ballery_notify_new_inquiry', 1);
    $auto_reply_enabled = get_option('ballery_auto_reply_enabled', 1);

    ?>
    <div class="wrap ballery-email-settings">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-email"></span>
            Email Settings
        </h1>

        <!-- Settings Tabs -->
        <div class="nav-tab-wrapper">
            <a href="#smtp-settings" class="nav-tab nav-tab-active" data-tab="smtp">SMTP Configuration</a>
            <a href="#email-settings" class="nav-tab" data-tab="email">Email Settings</a>
            <a href="#notifications" class="nav-tab" data-tab="notifications">Notifications</a>
            <a href="#test-email" class="nav-tab" data-tab="test">Test Email</a>
        </div>

        <form method="post">
            <!-- SMTP Settings Tab -->
            <div id="smtp-settings" class="tab-content active">
                <h2>SMTP Configuration</h2>
                <p>Configure SMTP settings for reliable email delivery. Recommended for production websites.</p>

                <table class="form-table">
                    <tr>
                        <th><label for="smtp-enabled">Enable SMTP</label></th>
                        <td>
                            <label>
                                <input type="checkbox" name="smtp_enabled" id="smtp-enabled" value="1"
                                       <?php checked($smtp_enabled, 1); ?>>
                                Use SMTP for sending emails
                            </label>
                            <p class="description">Enable this to use SMTP instead of PHP mail() function</p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="smtp-host">SMTP Host</label></th>
                        <td>
                            <input type="text" name="smtp_host" id="smtp-host"
                                   value="<?php echo esc_attr($smtp_host); ?>" class="regular-text">
                            <p class="description">e.g., smtp.gmail.com, smtp.outlook.com, mail.yourdomain.com</p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="smtp-port">SMTP Port</label></th>
                        <td>
                            <input type="number" name="smtp_port" id="smtp-port"
                                   value="<?php echo esc_attr($smtp_port); ?>" class="small-text">
                            <p class="description">Common ports: 587 (TLS), 465 (SSL), 25 (unsecured)</p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="smtp-encryption">Encryption</label></th>
                        <td>
                            <select name="smtp_encryption" id="smtp-encryption">
                                <option value="tls" <?php selected($smtp_encryption, 'tls'); ?>>TLS</option>
                                <option value="ssl" <?php selected($smtp_encryption, 'ssl'); ?>>SSL</option>
                                <option value="none" <?php selected($smtp_encryption, 'none'); ?>>None</option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="smtp-username">SMTP Username</label></th>
                        <td>
                            <input type="text" name="smtp_username" id="smtp-username"
                                   value="<?php echo esc_attr($smtp_username); ?>" class="regular-text">
                            <p class="description">Usually your email address</p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="smtp-password">SMTP Password</label></th>
                        <td>
                            <input type="password" name="smtp_password" id="smtp-password"
                                   value="<?php echo esc_attr($smtp_password); ?>" class="regular-text">
                            <p class="description">Your email password or app-specific password</p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Email Settings Tab -->
            <div id="email-settings" class="tab-content">
                <h2>Email Settings</h2>
                <p>Configure the sender information and recipient settings for your emails.</p>

                <table class="form-table">
                    <tr>
                        <th><label for="from-email">From Email</label></th>
                        <td>
                            <input type="email" name="from_email" id="from-email"
                                   value="<?php echo esc_attr($from_email); ?>" class="regular-text" required>
                            <p class="description">Email address that will appear as sender</p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="from-name">From Name</label></th>
                        <td>
                            <input type="text" name="from_name" id="from-name"
                                   value="<?php echo esc_attr($from_name); ?>" class="regular-text" required>
                            <p class="description">Name that will appear as sender</p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="admin-email">Admin Email</label></th>
                        <td>
                            <input type="email" name="admin_email" id="admin-email"
                                   value="<?php echo esc_attr($admin_email); ?>" class="regular-text" required>
                            <p class="description">Email address to receive notifications</p>
                        </td>
                    </tr>
                    <tr>
                        <th><label for="cc-emails">CC Emails</label></th>
                        <td>
                            <textarea name="cc_emails" id="cc-emails" rows="3" class="large-text"><?php echo esc_textarea($cc_emails); ?></textarea>
                            <p class="description">Additional email addresses to CC on notifications (one per line)</p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Notifications Tab -->
            <div id="notifications" class="tab-content">
                <h2>Notification Settings</h2>
                <p>Configure which email notifications to send automatically.</p>

                <table class="form-table">
                    <tr>
                        <th>Booking Notifications</th>
                        <td>
                            <label>
                                <input type="checkbox" name="notify_new_booking" value="1"
                                       <?php checked($notify_new_booking, 1); ?>>
                                Send email notification for new bookings
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th>Inquiry Notifications</th>
                        <td>
                            <label>
                                <input type="checkbox" name="notify_new_inquiry" value="1"
                                       <?php checked($notify_new_inquiry, 1); ?>>
                                Send email notification for new inquiries
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th>Auto-Reply</th>
                        <td>
                            <label>
                                <input type="checkbox" name="auto_reply_enabled" value="1"
                                       <?php checked($auto_reply_enabled, 1); ?>>
                                Send automatic reply to customers
                            </label>
                            <p class="description">Automatically send confirmation emails to customers</p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Test Email Tab -->
            <div id="test-email" class="tab-content">
                <h2>Test Email</h2>
                <p>Send a test email to verify your email settings are working correctly.</p>

                <table class="form-table">
                    <tr>
                        <th><label for="test-email-address">Test Email Address</label></th>
                        <td>
                            <input type="email" name="test_email" id="test-email-address"
                                   value="<?php echo esc_attr($admin_email); ?>" class="regular-text" required>
                            <p class="description">Email address to send test email to</p>
                        </td>
                    </tr>
                </table>

                <p class="submit">
                    <input type="submit" name="send_test_email" class="button button-secondary" value="Send Test Email">
                </p>
            </div>

            <p class="submit">
                <input type="submit" name="save_email_settings" class="button-primary" value="Save Email Settings">
            </p>
        </form>
    </div>

    <style>
    .ballery-email-settings .nav-tab-wrapper {
        margin: 20px 0;
    }

    .tab-content {
        display: none;
        padding: 20px 0;
    }

    .tab-content.active {
        display: block;
    }

    .tab-content h2 {
        margin-top: 0;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // Tab switching
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            var tab = $(this).data('tab');

            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');

            $('.tab-content').removeClass('active');
            $('#' + tab + '-settings, #' + tab + '-email, #notifications, #test-email').addClass('active');

            if (tab === 'smtp') {
                $('#smtp-settings').addClass('active');
            } else if (tab === 'email') {
                $('#email-settings').addClass('active');
            } else if (tab === 'notifications') {
                $('#notifications').addClass('active');
            } else if (tab === 'test') {
                $('#test-email').addClass('active');
            }
        });

        // SMTP toggle
        $('#smtp-enabled').on('change', function() {
            var smtpFields = $('#smtp-host, #smtp-port, #smtp-username, #smtp-password, #smtp-encryption');
            if (this.checked) {
                smtpFields.prop('required', true).closest('tr').show();
            } else {
                smtpFields.prop('required', false).closest('tr').show();
            }
        }).trigger('change');
    });
    </script>
    <?php
}

/**
 * Content Management Page
 */
function ballery_content_page() {
    // Handle form submissions
    if (isset($_POST['action'])) {
        $action = sanitize_text_field($_POST['action']);

        switch ($action) {
            case 'update_testimonial':
                $testimonial_id = intval($_POST['testimonial_id']);
                $client_name = sanitize_text_field($_POST['client_name']);
                $testimonial_text = sanitize_textarea_field($_POST['testimonial_text']);
                $rating = intval($_POST['rating']);
                $is_featured = isset($_POST['is_featured']) ? 1 : 0;

                // Update testimonial post
                wp_update_post([
                    'ID' => $testimonial_id,
                    'post_title' => $client_name,
                    'post_content' => $testimonial_text
                ]);

                update_post_meta($testimonial_id, 'rating', $rating);
                update_post_meta($testimonial_id, 'is_featured', $is_featured);

                echo '<div class="notice notice-success"><p>Testimonial updated successfully!</p></div>';
                break;

            case 'add_testimonial':
                $client_name = sanitize_text_field($_POST['client_name']);
                $testimonial_text = sanitize_textarea_field($_POST['testimonial_text']);
                $rating = intval($_POST['rating']);
                $is_featured = isset($_POST['is_featured']) ? 1 : 0;

                $post_id = wp_insert_post([
                    'post_title' => $client_name,
                    'post_content' => $testimonial_text,
                    'post_type' => 'testimonial',
                    'post_status' => 'publish'
                ]);

                if ($post_id) {
                    update_post_meta($post_id, 'rating', $rating);
                    update_post_meta($post_id, 'is_featured', $is_featured);
                    echo '<div class="notice notice-success"><p>Testimonial added successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to add testimonial.</p></div>';
                }
                break;

            case 'update_team_member':
                $member_id = intval($_POST['member_id']);
                $member_name = sanitize_text_field($_POST['member_name']);
                $position = sanitize_text_field($_POST['position']);
                $bio = sanitize_textarea_field($_POST['bio']);
                $experience = sanitize_text_field($_POST['experience']);

                wp_update_post([
                    'ID' => $member_id,
                    'post_title' => $member_name,
                    'post_content' => $bio
                ]);

                update_post_meta($member_id, 'position', $position);
                update_post_meta($member_id, 'experience', $experience);

                echo '<div class="notice notice-success"><p>Team member updated successfully!</p></div>';
                break;

            case 'add_team_member':
                $member_name = sanitize_text_field($_POST['member_name']);
                $position = sanitize_text_field($_POST['position']);
                $bio = sanitize_textarea_field($_POST['bio']);
                $experience = sanitize_text_field($_POST['experience']);

                $post_id = wp_insert_post([
                    'post_title' => $member_name,
                    'post_content' => $bio,
                    'post_type' => 'team_member',
                    'post_status' => 'publish'
                ]);

                if ($post_id) {
                    update_post_meta($post_id, 'position', $position);
                    update_post_meta($post_id, 'experience', $experience);
                    echo '<div class="notice notice-success"><p>Team member added successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to add team member.</p></div>';
                }
                break;
        }
    }

    // Get testimonials and team members
    $testimonials = get_posts([
        'post_type' => 'testimonial',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ]);

    $team_members = get_posts([
        'post_type' => 'team_member',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ]);

    ?>
    <div class="wrap ballery-content-page">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-admin-page"></span>
            Content Management
        </h1>

        <!-- Content Tabs -->
        <div class="nav-tab-wrapper">
            <a href="#testimonials" class="nav-tab nav-tab-active" data-tab="testimonials">Testimonials</a>
            <a href="#team-members" class="nav-tab" data-tab="team">Team Members</a>
            <a href="#faqs" class="nav-tab" data-tab="faqs">FAQs</a>
        </div>

        <!-- Testimonials Tab -->
        <div id="testimonials" class="tab-content active">
            <div class="tab-header">
                <h2>Customer Testimonials</h2>
                <button class="button button-primary" id="add-testimonial-btn">Add New Testimonial</button>
            </div>

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Client Name</th>
                        <th>Testimonial</th>
                        <th>Rating</th>
                        <th>Featured</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($testimonials as $testimonial): ?>
                    <?php
                    $rating = get_post_meta($testimonial->ID, 'rating', true);
                    $is_featured = get_post_meta($testimonial->ID, 'is_featured', true);
                    ?>
                    <tr>
                        <td><strong><?php echo esc_html($testimonial->post_title); ?></strong></td>
                        <td><?php echo esc_html(wp_trim_words($testimonial->post_content, 15)); ?></td>
                        <td>
                            <div class="rating">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <span class="star <?php echo $i <= $rating ? 'filled' : ''; ?>">★</span>
                                <?php endfor; ?>
                            </div>
                        </td>
                        <td>
                            <span class="featured-badge <?php echo $is_featured ? 'yes' : 'no'; ?>">
                                <?php echo $is_featured ? 'Yes' : 'No'; ?>
                            </span>
                        </td>
                        <td>
                            <button class="button button-small edit-testimonial"
                                    data-id="<?php echo $testimonial->ID; ?>"
                                    data-name="<?php echo esc_attr($testimonial->post_title); ?>"
                                    data-text="<?php echo esc_attr($testimonial->post_content); ?>"
                                    data-rating="<?php echo $rating; ?>"
                                    data-featured="<?php echo $is_featured; ?>">
                                Edit
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Team Members Tab -->
        <div id="team-members" class="tab-content">
            <div class="tab-header">
                <h2>Team Members</h2>
                <button class="button button-primary" id="add-team-member-btn">Add New Team Member</button>
            </div>

            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Position</th>
                        <th>Experience</th>
                        <th>Bio</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($team_members as $member): ?>
                    <?php
                    $position = get_post_meta($member->ID, 'position', true);
                    $experience = get_post_meta($member->ID, 'experience', true);
                    ?>
                    <tr>
                        <td><strong><?php echo esc_html($member->post_title); ?></strong></td>
                        <td><?php echo esc_html($position); ?></td>
                        <td><?php echo esc_html($experience); ?></td>
                        <td><?php echo esc_html(wp_trim_words($member->post_content, 10)); ?></td>
                        <td>
                            <button class="button button-small edit-team-member"
                                    data-id="<?php echo $member->ID; ?>"
                                    data-name="<?php echo esc_attr($member->post_title); ?>"
                                    data-position="<?php echo esc_attr($position); ?>"
                                    data-experience="<?php echo esc_attr($experience); ?>"
                                    data-bio="<?php echo esc_attr($member->post_content); ?>">
                                Edit
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- FAQs Tab -->
        <div id="faqs" class="tab-content">
            <h2>Frequently Asked Questions</h2>
            <p>Manage your FAQ section content. <a href="<?php echo admin_url('edit.php?post_type=page'); ?>">Edit FAQ page</a> directly in WordPress.</p>

            <div class="faq-suggestions">
                <h3>Suggested FAQ Topics:</h3>
                <ul>
                    <li>What services do you offer?</li>
                    <li>How much do consultations cost?</li>
                    <li>What areas do you serve?</li>
                    <li>How long does a consultation take?</li>
                    <li>Do you provide written reports?</li>
                    <li>What qualifications do your consultants have?</li>
                    <li>Can you help with permit applications?</li>
                    <li>Do you offer emergency consultations?</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Testimonial Modal -->
    <div id="testimonial-modal" class="ballery-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="testimonial-modal-title">Add New Testimonial</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="testimonial-form" method="post">
                    <input type="hidden" name="action" id="testimonial-action" value="add_testimonial">
                    <input type="hidden" name="testimonial_id" id="testimonial-id">

                    <table class="form-table">
                        <tr>
                            <th><label for="client-name">Client Name</label></th>
                            <td>
                                <input type="text" name="client_name" id="client-name"
                                       class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="testimonial-text">Testimonial</label></th>
                            <td>
                                <textarea name="testimonial_text" id="testimonial-text"
                                          rows="4" class="large-text" required></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="rating">Rating</label></th>
                            <td>
                                <select name="rating" id="rating" required>
                                    <option value="">Select Rating</option>
                                    <option value="5">5 Stars</option>
                                    <option value="4">4 Stars</option>
                                    <option value="3">3 Stars</option>
                                    <option value="2">2 Stars</option>
                                    <option value="1">1 Star</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th>Options</th>
                            <td>
                                <label>
                                    <input type="checkbox" name="is_featured" id="testimonial-featured" value="1">
                                    Featured Testimonial
                                </label>
                            </td>
                        </tr>
                    </table>

                    <p class="submit">
                        <input type="submit" class="button-primary" id="testimonial-submit" value="Add Testimonial">
                        <button type="button" class="button" onclick="jQuery('#testimonial-modal').hide();">Cancel</button>
                    </p>
                </form>
            </div>
        </div>
    </div>

    <!-- Team Member Modal -->
    <div id="team-member-modal" class="ballery-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="team-modal-title">Add New Team Member</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="team-member-form" method="post">
                    <input type="hidden" name="action" id="team-action" value="add_team_member">
                    <input type="hidden" name="member_id" id="member-id">

                    <table class="form-table">
                        <tr>
                            <th><label for="member-name">Name</label></th>
                            <td>
                                <input type="text" name="member_name" id="member-name"
                                       class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="position">Position</label></th>
                            <td>
                                <input type="text" name="position" id="position"
                                       class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="experience">Experience</label></th>
                            <td>
                                <input type="text" name="experience" id="experience"
                                       class="regular-text" placeholder="e.g., 10+ years">
                            </td>
                        </tr>
                        <tr>
                            <th><label for="bio">Bio</label></th>
                            <td>
                                <textarea name="bio" id="bio" rows="4" class="large-text"></textarea>
                            </td>
                        </tr>
                    </table>

                    <p class="submit">
                        <input type="submit" class="button-primary" id="team-submit" value="Add Team Member">
                        <button type="button" class="button" onclick="jQuery('#team-member-modal').hide();">Cancel</button>
                    </p>
                </form>
            </div>
        </div>
    </div>

    <style>
    .ballery-content-page .nav-tab-wrapper {
        margin: 20px 0;
    }

    .tab-content {
        display: none;
        padding: 20px 0;
    }

    .tab-content.active {
        display: block;
    }

    .tab-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .tab-header h2 {
        margin: 0;
    }

    .rating .star {
        color: #ddd;
        font-size: 16px;
    }

    .rating .star.filled {
        color: #ffc107;
    }

    .featured-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .featured-badge.yes {
        background: #d4edda;
        color: #155724;
    }

    .featured-badge.no {
        background: #f8d7da;
        color: #721c24;
    }

    .faq-suggestions {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
    }

    .faq-suggestions ul {
        list-style-type: disc;
        margin-left: 20px;
    }

    .ballery-modal {
        position: fixed;
        z-index: 100000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 0;
        border-radius: 8px;
        width: 80%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
    }

    .close {
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .modal-body {
        padding: 20px;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // Tab switching
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            var tab = $(this).data('tab');

            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');

            $('.tab-content').removeClass('active');
            $('#' + tab + ', #' + tab + '-members, #faqs').addClass('active');

            if (tab === 'testimonials') {
                $('#testimonials').addClass('active');
            } else if (tab === 'team') {
                $('#team-members').addClass('active');
            } else if (tab === 'faqs') {
                $('#faqs').addClass('active');
            }
        });

        // Testimonial management
        $('#add-testimonial-btn').on('click', function() {
            $('#testimonial-modal-title').text('Add New Testimonial');
            $('#testimonial-action').val('add_testimonial');
            $('#testimonial-submit').val('Add Testimonial');
            $('#testimonial-form')[0].reset();
            $('#testimonial-id').val('');
            $('#testimonial-modal').show();
        });

        $('.edit-testimonial').on('click', function() {
            var data = $(this).data();
            $('#testimonial-modal-title').text('Edit Testimonial');
            $('#testimonial-action').val('update_testimonial');
            $('#testimonial-submit').val('Update Testimonial');
            $('#testimonial-id').val(data.id);
            $('#client-name').val(data.name);
            $('#testimonial-text').val(data.text);
            $('#rating').val(data.rating);
            $('#testimonial-featured').prop('checked', data.featured == 1);
            $('#testimonial-modal').show();
        });

        // Team member management
        $('#add-team-member-btn').on('click', function() {
            $('#team-modal-title').text('Add New Team Member');
            $('#team-action').val('add_team_member');
            $('#team-submit').val('Add Team Member');
            $('#team-member-form')[0].reset();
            $('#member-id').val('');
            $('#team-member-modal').show();
        });

        $('.edit-team-member').on('click', function() {
            var data = $(this).data();
            $('#team-modal-title').text('Edit Team Member');
            $('#team-action').val('update_team_member');
            $('#team-submit').val('Update Team Member');
            $('#member-id').val(data.id);
            $('#member-name').val(data.name);
            $('#position').val(data.position);
            $('#experience').val(data.experience);
            $('#bio').val(data.bio);
            $('#team-member-modal').show();
        });

        // Close modals
        $('.close, .ballery-modal').on('click', function(e) {
            if (e.target === this) {
                $('.ballery-modal').hide();
            }
        });
    });
    </script>
    <?php
}

/**
 * Reports & Analytics Page
 */
function ballery_reports_page() {
    global $wpdb;

    // Get date range from request
    $date_range = isset($_GET['range']) ? sanitize_text_field($_GET['range']) : '30';
    $start_date = date('Y-m-d', strtotime("-{$date_range} days"));
    $end_date = date('Y-m-d');

    // Database tables
    $bookings_table = $wpdb->prefix . 'ballery_bookings';
    $inquiries_table = $wpdb->prefix . 'ballery_inquiries';

    // Booking statistics
    $total_bookings = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $bookings_table WHERE DATE(booking_date) BETWEEN %s AND %s",
        $start_date, $end_date
    ));

    $confirmed_bookings = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $bookings_table WHERE status = 'confirmed' AND DATE(booking_date) BETWEEN %s AND %s",
        $start_date, $end_date
    ));

    $completed_bookings = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $bookings_table WHERE status = 'completed' AND DATE(booking_date) BETWEEN %s AND %s",
        $start_date, $end_date
    ));

    $total_revenue = $wpdb->get_var($wpdb->prepare(
        "SELECT SUM(total_amount) FROM $bookings_table WHERE status IN ('confirmed', 'completed') AND DATE(booking_date) BETWEEN %s AND %s",
        $start_date, $end_date
    ));

    $avg_booking_value = $total_bookings > 0 ? $total_revenue / $total_bookings : 0;

    // Service type breakdown
    $service_breakdown = $wpdb->get_results($wpdb->prepare(
        "SELECT service_type, COUNT(*) as count, SUM(total_amount) as revenue
         FROM $bookings_table
         WHERE DATE(booking_date) BETWEEN %s AND %s
         GROUP BY service_type",
        $start_date, $end_date
    ));

    // Monthly trends
    $monthly_data = $wpdb->get_results($wpdb->prepare(
        "SELECT
            DATE_FORMAT(booking_date, '%%Y-%%m') as month,
            COUNT(*) as bookings,
            SUM(total_amount) as revenue
         FROM $bookings_table
         WHERE DATE(booking_date) BETWEEN %s AND %s
         GROUP BY DATE_FORMAT(booking_date, '%%Y-%%m')
         ORDER BY month",
        $start_date, $end_date
    ));

    // Inquiry statistics
    $total_inquiries = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $inquiries_table WHERE DATE(created_at) BETWEEN %s AND %s",
        $start_date, $end_date
    ));

    $responded_inquiries = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $inquiries_table WHERE status = 'responded' AND DATE(created_at) BETWEEN %s AND %s",
        $start_date, $end_date
    ));

    $response_rate = $total_inquiries > 0 ? ($responded_inquiries / $total_inquiries) * 100 : 0;

    // Top performing services
    $top_services = $wpdb->get_results($wpdb->prepare(
        "SELECT
            sp.service_name,
            COUNT(b.id) as bookings,
            SUM(b.total_amount) as revenue
         FROM {$wpdb->prefix}ballery_service_pricing sp
         LEFT JOIN $bookings_table b ON JSON_CONTAINS(b.services, JSON_QUOTE(sp.service_name))
         WHERE b.booking_date IS NULL OR DATE(b.booking_date) BETWEEN %s AND %s
         GROUP BY sp.service_name
         ORDER BY revenue DESC
         LIMIT 10",
        $start_date, $end_date
    ));

    ?>
    <div class="wrap ballery-reports-page">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-chart-area"></span>
            Reports & Analytics
        </h1>

        <!-- Date Range Filter -->
        <div class="date-range-filter">
            <label for="date-range">Date Range:</label>
            <select id="date-range" onchange="window.location.href='?page=ballery-reports&range=' + this.value;">
                <option value="7" <?php selected($date_range, '7'); ?>>Last 7 days</option>
                <option value="30" <?php selected($date_range, '30'); ?>>Last 30 days</option>
                <option value="90" <?php selected($date_range, '90'); ?>>Last 90 days</option>
                <option value="365" <?php selected($date_range, '365'); ?>>Last year</option>
            </select>
            <span class="date-display"><?php echo date('M j, Y', strtotime($start_date)) . ' - ' . date('M j, Y', strtotime($end_date)); ?></span>
        </div>

        <!-- Key Metrics -->
        <div class="metrics-grid">
            <div class="metric-card bookings">
                <div class="metric-icon">📅</div>
                <div class="metric-content">
                    <h3><?php echo number_format($total_bookings); ?></h3>
                    <p>Total Bookings</p>
                    <small><?php echo number_format($confirmed_bookings); ?> confirmed</small>
                </div>
            </div>

            <div class="metric-card revenue">
                <div class="metric-icon">💰</div>
                <div class="metric-content">
                    <h3>₹<?php echo number_format($total_revenue, 2); ?></h3>
                    <p>Total Revenue</p>
                    <small>Avg: ₹<?php echo number_format($avg_booking_value, 2); ?></small>
                </div>
            </div>

            <div class="metric-card completion">
                <div class="metric-icon">✅</div>
                <div class="metric-content">
                    <h3><?php echo number_format($completed_bookings); ?></h3>
                    <p>Completed Projects</p>
                    <small><?php echo $total_bookings > 0 ? number_format(($completed_bookings / $total_bookings) * 100, 1) : 0; ?>% completion rate</small>
                </div>
            </div>

            <div class="metric-card inquiries">
                <div class="metric-icon">💬</div>
                <div class="metric-content">
                    <h3><?php echo number_format($total_inquiries); ?></h3>
                    <p>Customer Inquiries</p>
                    <small><?php echo number_format($response_rate, 1); ?>% response rate</small>
                </div>
            </div>
        </div>

        <!-- Charts and Analysis -->
        <div class="reports-grid">
            <!-- Service Type Breakdown -->
            <div class="report-section">
                <h2>Service Type Performance</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Service Type</th>
                            <th>Bookings</th>
                            <th>Revenue</th>
                            <th>Avg Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($service_breakdown as $service): ?>
                        <tr>
                            <td><strong><?php echo esc_html(ucwords(str_replace('-', ' ', $service->service_type))); ?></strong></td>
                            <td><?php echo number_format($service->count); ?></td>
                            <td>₹<?php echo number_format($service->revenue, 2); ?></td>
                            <td>₹<?php echo number_format($service->revenue / $service->count, 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Monthly Trends -->
            <div class="report-section">
                <h2>Monthly Trends</h2>
                <div class="chart-container">
                    <canvas id="monthly-chart" width="400" height="200"></canvas>
                </div>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Month</th>
                            <th>Bookings</th>
                            <th>Revenue</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($monthly_data as $month): ?>
                        <tr>
                            <td><?php echo date('F Y', strtotime($month->month . '-01')); ?></td>
                            <td><?php echo number_format($month->bookings); ?></td>
                            <td>₹<?php echo number_format($month->revenue, 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Top Services -->
            <div class="report-section">
                <h2>Top Performing Services</h2>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Service Name</th>
                            <th>Bookings</th>
                            <th>Revenue</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($top_services as $service): ?>
                        <tr>
                            <td><strong><?php echo esc_html($service->service_name); ?></strong></td>
                            <td><?php echo number_format($service->bookings); ?></td>
                            <td>₹<?php echo number_format($service->revenue, 2); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Export Options -->
        <div class="export-section">
            <h2>Export Data</h2>
            <p>Download reports for external analysis or record keeping.</p>
            <div class="export-buttons">
                <a href="?page=ballery-reports&export=bookings&range=<?php echo $date_range; ?>"
                   class="button button-secondary">
                    <span class="dashicons dashicons-download"></span> Export Bookings CSV
                </a>
                <a href="?page=ballery-reports&export=inquiries&range=<?php echo $date_range; ?>"
                   class="button button-secondary">
                    <span class="dashicons dashicons-download"></span> Export Inquiries CSV
                </a>
                <a href="?page=ballery-reports&export=revenue&range=<?php echo $date_range; ?>"
                   class="button button-secondary">
                    <span class="dashicons dashicons-download"></span> Export Revenue Report
                </a>
            </div>
        </div>
    </div>

    <style>
    .ballery-reports-page {
        background: #f1f1f1;
        margin: -20px -20px 0 -22px;
        padding: 20px;
    }

    .date-range-filter {
        background: white;
        padding: 15px 20px;
        margin: 20px 0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .date-display {
        color: #666;
        font-style: italic;
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .metric-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .metric-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .metric-content h3 {
        margin: 0;
        font-size: 2rem;
        font-weight: bold;
        color: #2c5aa0;
    }

    .metric-content p {
        margin: 5px 0;
        font-weight: 600;
        color: #333;
    }

    .metric-content small {
        color: #666;
    }

    .reports-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }

    .report-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .report-section h2 {
        margin-top: 0;
        margin-bottom: 20px;
        color: #2c5aa0;
    }

    .chart-container {
        margin: 20px 0;
        text-align: center;
    }

    .export-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .export-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .export-buttons .button {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    @media (max-width: 768px) {
        .reports-grid {
            grid-template-columns: 1fr;
        }

        .metrics-grid {
            grid-template-columns: 1fr;
        }
    }
    </style>

    <script>
    // Simple chart implementation (you can replace with Chart.js or similar)
    document.addEventListener('DOMContentLoaded', function() {
        var canvas = document.getElementById('monthly-chart');
        if (canvas) {
            var ctx = canvas.getContext('2d');

            // Sample chart - replace with actual chart library
            ctx.fillStyle = '#2c5aa0';
            ctx.fillRect(50, 150, 30, 50);
            ctx.fillRect(100, 120, 30, 80);
            ctx.fillRect(150, 100, 30, 100);
            ctx.fillRect(200, 80, 30, 120);

            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('Revenue Trend', 150, 20);
            ctx.fillText('(Implement Chart.js for detailed charts)', 100, 40);
        }
    });
    </script>
    <?php

    // Handle CSV exports
    if (isset($_GET['export'])) {
        ballery_handle_export($_GET['export'], $date_range);
    }
}

/**
 * Handle CSV exports
 */
function ballery_handle_export($type, $date_range) {
    global $wpdb;

    $start_date = date('Y-m-d', strtotime("-{$date_range} days"));
    $end_date = date('Y-m-d');

    switch ($type) {
        case 'bookings':
            $bookings_table = $wpdb->prefix . 'ballery_bookings';
            $data = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $bookings_table WHERE DATE(booking_date) BETWEEN %s AND %s ORDER BY booking_date DESC",
                $start_date, $end_date
            ), ARRAY_A);

            ballery_export_csv($data, 'bookings_' . $start_date . '_to_' . $end_date . '.csv');
            break;

        case 'inquiries':
            $inquiries_table = $wpdb->prefix . 'ballery_inquiries';
            $data = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $inquiries_table WHERE DATE(created_at) BETWEEN %s AND %s ORDER BY created_at DESC",
                $start_date, $end_date
            ), ARRAY_A);

            ballery_export_csv($data, 'inquiries_' . $start_date . '_to_' . $end_date . '.csv');
            break;

        case 'revenue':
            $bookings_table = $wpdb->prefix . 'ballery_bookings';
            $data = $wpdb->get_results($wpdb->prepare(
                "SELECT booking_id, client_name, service_type, total_amount, status, booking_date
                 FROM $bookings_table
                 WHERE DATE(booking_date) BETWEEN %s AND %s
                 ORDER BY booking_date DESC",
                $start_date, $end_date
            ), ARRAY_A);

            ballery_export_csv($data, 'revenue_report_' . $start_date . '_to_' . $end_date . '.csv');
            break;
    }
}

/**
 * Export data as CSV
 */
function ballery_export_csv($data, $filename) {
    if (empty($data)) {
        wp_die('No data to export for the selected date range.');
    }

    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    $output = fopen('php://output', 'w');

    // Add CSV headers
    fputcsv($output, array_keys($data[0]));

    // Add data rows
    foreach ($data as $row) {
        fputcsv($output, $row);
    }

    fclose($output);
    exit;
}
