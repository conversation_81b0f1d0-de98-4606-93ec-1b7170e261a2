<?php
/**
 * WordPress Automated Setup Script for BALLERY.IN
 * This script helps automate the WordPress installation process
 * 
 * @package BALLERY.IN
 */

// Configuration
$config = [
    'db_name' => 'ballery_db',
    'db_user' => 'root',
    'db_password' => '',
    'db_host' => 'localhost',
    'site_title' => 'BALLERY.IN',
    'admin_user' => 'admin',
    'admin_password' => 'BalleryAdmin2024!',
    'admin_email' => '<EMAIL>',
    'site_url' => 'http://localhost/ballery'
];

/**
 * Create wp-config.php file
 */
function create_wp_config($config) {
    $wp_config_content = "<?php
/**
 * The base configuration for WordPress
 *
 * @package WordPress
 */

// ** MySQL settings ** //
define('DB_NAME', '{$config['db_name']}');
define('DB_USER', '{$config['db_user']}');
define('DB_PASSWORD', '{$config['db_password']}');
define('DB_HOST', '{$config['db_host']}');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// ** Authentication Unique Keys and Salts ** //
define('AUTH_KEY',         'put your unique phrase here');
define('SECURE_AUTH_KEY',  'put your unique phrase here');
define('LOGGED_IN_KEY',    'put your unique phrase here');
define('NONCE_KEY',        'put your unique phrase here');
define('AUTH_SALT',        'put your unique phrase here');
define('SECURE_AUTH_SALT', 'put your unique phrase here');
define('LOGGED_IN_SALT',   'put your unique phrase here');
define('NONCE_SALT',       'put your unique phrase here');

// ** WordPress Database Table prefix ** //
\$table_prefix = 'wp_';

// ** WordPress debugging mode ** //
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);

// ** Absolute path to the WordPress directory ** //
if (!defined('ABSPATH')) {
    define('ABSPATH', __DIR__ . '/');
}

// ** Sets up WordPress vars and included files ** //
require_once ABSPATH . 'wp-settings.php';
";

    return file_put_contents('wp-config.php', $wp_config_content);
}

/**
 * Create database if it doesn't exist
 */
function create_database($config) {
    try {
        $pdo = new PDO("mysql:host={$config['db_host']}", $config['db_user'], $config['db_password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $sql = "CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        $pdo->exec($sql);
        
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Create required WordPress pages
 */
function create_wordpress_pages() {
    $pages = [
        'About' => 'Learn more about BALLERY.IN and our expert construction consultation services.',
        'Services' => 'Explore our comprehensive range of construction consultation services.',
        'Pricing' => 'Transparent pricing for all our construction consultation services.',
        'Contact' => 'Get in touch with our construction experts for any queries.',
        'Book Consultation' => 'Schedule your professional construction consultation today.'
    ];
    
    foreach ($pages as $title => $content) {
        $page_data = [
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_author' => 1
        ];
        
        wp_insert_post($page_data);
    }
}

/**
 * Setup WordPress theme and basic configuration
 */
function setup_wordpress_theme() {
    // Switch to BALLERY theme
    switch_theme('ballery');
    
    // Set permalink structure
    update_option('permalink_structure', '/%postname%/');
    
    // Set site options
    update_option('blogname', 'BALLERY.IN');
    update_option('blogdescription', 'Your On-Demand Construction Expert');
    update_option('start_of_week', 1);
    update_option('timezone_string', 'Asia/Kolkata');
    
    // Create navigation menu
    $menu_name = 'Primary Menu';
    $menu_id = wp_create_nav_menu($menu_name);
    
    if (!is_wp_error($menu_id)) {
        // Add menu items
        $pages = ['Home', 'Services', 'Pricing', 'About', 'Contact', 'Book Consultation'];
        
        foreach ($pages as $page) {
            if ($page === 'Home') {
                wp_update_nav_menu_item($menu_id, 0, [
                    'menu-item-title' => 'Home',
                    'menu-item-url' => home_url('/'),
                    'menu-item-status' => 'publish'
                ]);
            } else {
                $page_obj = get_page_by_title($page);
                if ($page_obj) {
                    wp_update_nav_menu_item($menu_id, 0, [
                        'menu-item-object-id' => $page_obj->ID,
                        'menu-item-object' => 'page',
                        'menu-item-type' => 'post_type',
                        'menu-item-status' => 'publish'
                    ]);
                }
            }
        }
        
        // Assign menu to theme location
        $locations = get_theme_mod('nav_menu_locations');
        $locations['primary'] = $menu_id;
        set_theme_mod('nav_menu_locations', $locations);
    }
}

// Main execution
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    echo "<h2>BALLERY.IN WordPress Setup</h2>";
    
    // Check if WordPress is already installed
    if (file_exists('wp-config.php')) {
        echo "<p style='color: orange;'>⚠️ WordPress appears to be already configured.</p>";
        echo "<p>If you want to proceed anyway, please delete wp-config.php first.</p>";
        exit;
    }
    
    echo "<h3>Setup Progress:</h3>";
    echo "<ul>";
    
    // Step 1: Create database
    echo "<li>Creating database... ";
    if (create_database($config)) {
        echo "✅ SUCCESS</li>";
    } else {
        echo "❌ FAILED - Please check your MySQL connection</li>";
        exit;
    }
    
    // Step 2: Create wp-config.php
    echo "<li>Creating wp-config.php... ";
    if (create_wp_config($config)) {
        echo "✅ SUCCESS</li>";
    } else {
        echo "❌ FAILED - Check file permissions</li>";
        exit;
    }
    
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Visit <a href='{$config['site_url']}/wp-admin/install.php' target='_blank'>{$config['site_url']}/wp-admin/install.php</a></li>";
    echo "<li>Complete the WordPress installation with these details:</li>";
    echo "<ul>";
    echo "<li><strong>Site Title:</strong> {$config['site_title']}</li>";
    echo "<li><strong>Username:</strong> {$config['admin_user']}</li>";
    echo "<li><strong>Password:</strong> {$config['admin_password']}</li>";
    echo "<li><strong>Email:</strong> {$config['admin_email']}</li>";
    echo "</ul>";
    echo "<li>After installation, run the database setup: <a href='setup-database.php' target='_blank'>setup-database.php</a></li>";
    echo "<li>Activate the BALLERY.IN theme in WordPress admin</li>";
    echo "</ol>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #2196F3; margin: 20px 0;'>";
    echo "<h4>🔐 Important Security Information:</h4>";
    echo "<p><strong>Admin Credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> {$config['admin_user']}</li>";
    echo "<li><strong>Password:</strong> {$config['admin_password']}</li>";
    echo "</ul>";
    echo "<p><em>Please change the admin password after first login for security.</em></p>";
    echo "</div>";
    
    echo "<p style='color: red; font-weight: bold;'>🗑️ Remember to delete this setup file after installation for security!</p>";
}

?>
