# BALLERY.IN Local Development Setup Script
# PowerShell version for enhanced functionality

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "BALLERY.IN Local Development Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    }
    catch {
        return $false
    }
}

# Function to download file
function Download-File {
    param([string]$Url, [string]$Output)
    try {
        Invoke-WebRequest -Uri $Url -OutFile $Output -UseBasicParsing
        return $true
    }
    catch {
        Write-Host "Failed to download: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Check if XAMPP is installed
$xamppPath = "C:\xampp"
$xamppControl = "$xamppPath\xampp-control.exe"

if (Test-Path $xamppControl) {
    Write-Host "[SUCCESS] XAMPP found at $xamppPath" -ForegroundColor Green
} else {
    Write-Host "[WARNING] XAMPP not found at $xamppPath" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "XAMPP Installation Required:" -ForegroundColor Yellow
    Write-Host "1. Download XAMPP from: https://www.apachefriends.org/download.html"
    Write-Host "2. Install XAMPP to C:\xampp (default location)"
    Write-Host "3. Run this script again"
    Write-Host ""
    
    $response = Read-Host "Would you like to open the XAMPP download page? (y/n)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        Start-Process "https://www.apachefriends.org/download.html"
    }
    
    Write-Host "Press any key to exit..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

# Start XAMPP Control Panel
Write-Host "[INFO] Starting XAMPP Control Panel..." -ForegroundColor Blue
Start-Process $xamppControl

Write-Host ""
Write-Host "Please start Apache and MySQL services in XAMPP Control Panel" -ForegroundColor Yellow
Write-Host "Press any key when services are started..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Check if services are running
Write-Host ""
Write-Host "[INFO] Checking services..." -ForegroundColor Blue

$apacheRunning = Test-Port -Port 80
$mysqlRunning = Test-Port -Port 3306

if ($apacheRunning) {
    Write-Host "[SUCCESS] Apache is running on port 80" -ForegroundColor Green
} else {
    Write-Host "[ERROR] Apache is not running. Please start it in XAMPP Control Panel" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if ($mysqlRunning) {
    Write-Host "[SUCCESS] MySQL is running on port 3306" -ForegroundColor Green
} else {
    Write-Host "[ERROR] MySQL is not running. Please start it in XAMPP Control Panel" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "[SUCCESS] XAMPP services are running!" -ForegroundColor Green
Write-Host ""

# Setup WordPress and theme
$htdocsPath = "C:\xampp\htdocs\ballery"
$wpConfigPath = "$htdocsPath\wp-config.php"

# Create ballery directory
if (!(Test-Path $htdocsPath)) {
    Write-Host "[INFO] Creating ballery directory..." -ForegroundColor Blue
    New-Item -ItemType Directory -Path $htdocsPath -Force | Out-Null
}

# Copy theme files
Write-Host "[INFO] Copying BALLERY.IN theme files..." -ForegroundColor Blue
$currentPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Copy-Item -Path "$currentPath\*" -Destination $htdocsPath -Recurse -Force

# Check if WordPress is installed
$wpAdminPath = "$htdocsPath\wp-admin"
if (!(Test-Path $wpAdminPath)) {
    Write-Host ""
    Write-Host "[INFO] WordPress not found. Attempting to download..." -ForegroundColor Blue
    
    $wpZipPath = "$htdocsPath\wordpress.zip"
    $wpUrl = "https://wordpress.org/latest.zip"
    
    Write-Host "Downloading WordPress..." -ForegroundColor Blue
    if (Download-File -Url $wpUrl -Output $wpZipPath) {
        Write-Host "Extracting WordPress..." -ForegroundColor Blue
        try {
            Expand-Archive -Path $wpZipPath -DestinationPath $htdocsPath -Force
            
            # Move WordPress files from wordpress subfolder to root
            $wpSubfolder = "$htdocsPath\wordpress"
            if (Test-Path $wpSubfolder) {
                Get-ChildItem -Path $wpSubfolder | Move-Item -Destination $htdocsPath -Force
                Remove-Item -Path $wpSubfolder -Recurse -Force
            }
            
            Remove-Item -Path $wpZipPath -Force
            Write-Host "[SUCCESS] WordPress downloaded and extracted" -ForegroundColor Green
        }
        catch {
            Write-Host "[ERROR] Failed to extract WordPress: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Please download WordPress manually from https://wordpress.org/download/" -ForegroundColor Yellow
            Start-Process "https://wordpress.org/download/"
            Read-Host "Press Enter after downloading and extracting WordPress to $htdocsPath"
        }
    } else {
        Write-Host "[ERROR] Failed to download WordPress" -ForegroundColor Red
        Write-Host "Please download WordPress manually from https://wordpress.org/download/" -ForegroundColor Yellow
        Start-Process "https://wordpress.org/download/"
        Read-Host "Press Enter after downloading and extracting WordPress to $htdocsPath"
    }
}

# Check if WordPress is configured
if (Test-Path $wpConfigPath) {
    Write-Host "[INFO] WordPress appears to be configured" -ForegroundColor Blue
} else {
    Write-Host ""
    Write-Host "[INFO] Setting up WordPress..." -ForegroundColor Blue
    Write-Host "Opening WordPress setup in browser..." -ForegroundColor Blue
    Start-Process "http://localhost/ballery/wordpress-setup.php"
    
    Write-Host ""
    Write-Host "Please complete the WordPress setup in the browser, then press any key..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Final setup steps
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Final Setup Steps" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Running database setup..." -ForegroundColor Blue
Start-Process "http://localhost/ballery/setup-database.php"

Write-Host ""
Write-Host "Opening verification page..." -ForegroundColor Blue
Start-Process "http://localhost/ballery/verify-setup.php"

# Open the site
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "BALLERY.IN Local Site Ready!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Your local development environment is ready!" -ForegroundColor Green
Write-Host ""
Write-Host "Access URLs:" -ForegroundColor White
Write-Host "- Website: http://localhost/ballery" -ForegroundColor Cyan
Write-Host "- WordPress Admin: http://localhost/ballery/wp-admin" -ForegroundColor Cyan
Write-Host "- phpMyAdmin: http://localhost/phpmyadmin" -ForegroundColor Cyan
Write-Host ""
Write-Host "Default WordPress Login:" -ForegroundColor White
Write-Host "- Username: admin" -ForegroundColor Yellow
Write-Host "- Password: BalleryAdmin2024!" -ForegroundColor Yellow
Write-Host ""

Write-Host "Opening your site..." -ForegroundColor Blue
Start-Process "http://localhost/ballery"

Write-Host ""
Write-Host "[SUCCESS] Setup complete! Your BALLERY.IN site is now running locally." -ForegroundColor Green
Write-Host ""
Read-Host "Press Enter to exit"
