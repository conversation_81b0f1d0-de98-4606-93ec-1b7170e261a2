<?php
/**
 * Setup Verification Script for BALLERY.IN
 * This script checks if all components are properly installed and configured
 * 
 * @package BALLERY.IN
 */

// Prevent direct access in production
if (!defined('ABSPATH')) {
    // If not in WordPress context, try to load WordPress
    if (file_exists('wp-config.php')) {
        require_once('wp-config.php');
        require_once(ABSPATH . 'wp-settings.php');
    } else {
        // Standalone mode for initial verification
        $standalone = true;
    }
}

/**
 * Check XAMPP Services
 */
function check_xampp_services() {
    $results = [];
    
    // Check if we can connect to MySQL
    try {
        $pdo = new PDO("mysql:host=localhost", 'root', '');
        $results['mysql'] = true;
    } catch (PDOException $e) {
        $results['mysql'] = false;
    }
    
    // Check if Apache is running (we're here, so it probably is)
    $results['apache'] = true;
    
    return $results;
}

/**
 * Check WordPress Installation
 */
function check_wordpress() {
    $results = [];
    
    // Check if wp-config.php exists
    $results['wp_config'] = file_exists('wp-config.php');
    
    // Check if WordPress is installed
    if (defined('ABSPATH')) {
        $results['wp_installed'] = true;
        $results['wp_version'] = get_bloginfo('version');
        $results['site_url'] = get_site_url();
        $results['admin_url'] = admin_url();
    } else {
        $results['wp_installed'] = false;
    }
    
    return $results;
}

/**
 * Check Database Tables
 */
function check_database_tables() {
    global $wpdb;
    
    if (!isset($wpdb)) {
        return ['error' => 'WordPress database not available'];
    }
    
    $results = [];
    
    // Check WordPress core tables
    $wp_tables = ['posts', 'users', 'options'];
    foreach ($wp_tables as $table) {
        $table_name = $wpdb->prefix . $table;
        $results['wp_' . $table] = ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name);
    }
    
    // Check BALLERY custom tables
    $ballery_tables = [
        'ballery_bookings',
        'ballery_booking_files', 
        'ballery_service_pricing'
    ];
    
    foreach ($ballery_tables as $table) {
        $table_name = $wpdb->prefix . $table;
        $results[$table] = ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name);
    }
    
    return $results;
}

/**
 * Check Theme Files
 */
function check_theme_files() {
    $required_files = [
        'style.css',
        'index.php',
        'functions.php',
        'header.php',
        'footer.php',
        'page-about.php',
        'page-services.php',
        'page-pricing.php',
        'page-contact.php',
        'page-book-consultation.php'
    ];
    
    $results = [];
    
    foreach ($required_files as $file) {
        $results[$file] = file_exists($file);
    }
    
    return $results;
}

/**
 * Check WordPress Pages
 */
function check_wordpress_pages() {
    if (!function_exists('get_page_by_title')) {
        return ['error' => 'WordPress not loaded'];
    }
    
    $required_pages = ['About', 'Services', 'Pricing', 'Contact', 'Book Consultation'];
    $results = [];
    
    foreach ($required_pages as $page_title) {
        $page = get_page_by_title($page_title);
        $results[strtolower(str_replace(' ', '_', $page_title))] = ($page !== null);
    }
    
    return $results;
}

/**
 * Check Email Configuration
 */
function check_email_config() {
    if (!function_exists('wp_mail')) {
        return ['error' => 'WordPress mail functions not available'];
    }
    
    $results = [];
    $results['admin_email'] = get_option('admin_email');
    $results['mail_function'] = function_exists('mail');
    
    return $results;
}

// Main execution
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>BALLERY.IN Setup Verification</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .success { color: #28a745; }
            .error { color: #dc3545; }
            .warning { color: #ffc107; }
            .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007cba; background: #f8f9fa; }
            table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background: #f8f9fa; font-weight: bold; }
            .status-ok { color: #28a745; font-weight: bold; }
            .status-fail { color: #dc3545; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔍 BALLERY.IN Setup Verification</h1>
            <p>This script checks if all components are properly installed and configured.</p>
            
            <?php
            // Run all checks
            $xampp_status = check_xampp_services();
            $wp_status = check_wordpress();
            $db_status = check_database_tables();
            $theme_status = check_theme_files();
            
            if (isset($wp_status['wp_installed']) && $wp_status['wp_installed']) {
                $pages_status = check_wordpress_pages();
                $email_status = check_email_config();
            }
            ?>
            
            <div class="section">
                <h2>📊 XAMPP Services</h2>
                <table>
                    <tr><th>Service</th><th>Status</th></tr>
                    <tr><td>Apache Web Server</td><td class="<?php echo $xampp_status['apache'] ? 'status-ok' : 'status-fail'; ?>"><?php echo $xampp_status['apache'] ? '✅ Running' : '❌ Not Running'; ?></td></tr>
                    <tr><td>MySQL Database</td><td class="<?php echo $xampp_status['mysql'] ? 'status-ok' : 'status-fail'; ?>"><?php echo $xampp_status['mysql'] ? '✅ Connected' : '❌ Connection Failed'; ?></td></tr>
                </table>
            </div>
            
            <div class="section">
                <h2>🌐 WordPress Installation</h2>
                <table>
                    <tr><th>Component</th><th>Status</th></tr>
                    <tr><td>wp-config.php</td><td class="<?php echo $wp_status['wp_config'] ? 'status-ok' : 'status-fail'; ?>"><?php echo $wp_status['wp_config'] ? '✅ Found' : '❌ Missing'; ?></td></tr>
                    <tr><td>WordPress Core</td><td class="<?php echo $wp_status['wp_installed'] ? 'status-ok' : 'status-fail'; ?>"><?php echo $wp_status['wp_installed'] ? '✅ Installed' : '❌ Not Installed'; ?></td></tr>
                    <?php if ($wp_status['wp_installed']): ?>
                    <tr><td>WordPress Version</td><td class="status-ok"><?php echo $wp_status['wp_version']; ?></td></tr>
                    <tr><td>Site URL</td><td><a href="<?php echo $wp_status['site_url']; ?>" target="_blank"><?php echo $wp_status['site_url']; ?></a></td></tr>
                    <tr><td>Admin URL</td><td><a href="<?php echo $wp_status['admin_url']; ?>" target="_blank"><?php echo $wp_status['admin_url']; ?></a></td></tr>
                    <?php endif; ?>
                </table>
            </div>
            
            <div class="section">
                <h2>🗄️ Database Tables</h2>
                <?php if (isset($db_status['error'])): ?>
                    <p class="error">❌ <?php echo $db_status['error']; ?></p>
                <?php else: ?>
                <table>
                    <tr><th>Table</th><th>Status</th></tr>
                    <?php foreach ($db_status as $table => $exists): ?>
                    <tr><td><?php echo $table; ?></td><td class="<?php echo $exists ? 'status-ok' : 'status-fail'; ?>"><?php echo $exists ? '✅ Exists' : '❌ Missing'; ?></td></tr>
                    <?php endforeach; ?>
                </table>
                <?php endif; ?>
            </div>
            
            <div class="section">
                <h2>🎨 Theme Files</h2>
                <table>
                    <tr><th>File</th><th>Status</th></tr>
                    <?php foreach ($theme_status as $file => $exists): ?>
                    <tr><td><?php echo $file; ?></td><td class="<?php echo $exists ? 'status-ok' : 'status-fail'; ?>"><?php echo $exists ? '✅ Found' : '❌ Missing'; ?></td></tr>
                    <?php endforeach; ?>
                </table>
            </div>
            
            <?php if (isset($pages_status)): ?>
            <div class="section">
                <h2>📄 WordPress Pages</h2>
                <?php if (isset($pages_status['error'])): ?>
                    <p class="error">❌ <?php echo $pages_status['error']; ?></p>
                <?php else: ?>
                <table>
                    <tr><th>Page</th><th>Status</th></tr>
                    <?php foreach ($pages_status as $page => $exists): ?>
                    <tr><td><?php echo ucwords(str_replace('_', ' ', $page)); ?></td><td class="<?php echo $exists ? 'status-ok' : 'status-fail'; ?>"><?php echo $exists ? '✅ Created' : '❌ Missing'; ?></td></tr>
                    <?php endforeach; ?>
                </table>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <?php if (isset($email_status)): ?>
            <div class="section">
                <h2>📧 Email Configuration</h2>
                <?php if (isset($email_status['error'])): ?>
                    <p class="error">❌ <?php echo $email_status['error']; ?></p>
                <?php else: ?>
                <table>
                    <tr><th>Setting</th><th>Value</th></tr>
                    <tr><td>Admin Email</td><td><?php echo $email_status['admin_email']; ?></td></tr>
                    <tr><td>Mail Function</td><td class="<?php echo $email_status['mail_function'] ? 'status-ok' : 'status-fail'; ?>"><?php echo $email_status['mail_function'] ? '✅ Available' : '❌ Not Available'; ?></td></tr>
                </table>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            
            <div class="section">
                <h2>🚀 Next Steps</h2>
                <?php
                $all_good = true;
                
                // Check critical components
                if (!$xampp_status['apache'] || !$xampp_status['mysql']) {
                    echo "<p class='error'>❌ XAMPP services are not running properly. Please start Apache and MySQL.</p>";
                    $all_good = false;
                }
                
                if (!$wp_status['wp_installed']) {
                    echo "<p class='error'>❌ WordPress is not installed. Please complete the WordPress installation first.</p>";
                    $all_good = false;
                }
                
                if (isset($db_status['ballery_bookings']) && !$db_status['ballery_bookings']) {
                    echo "<p class='warning'>⚠️ Booking system tables are missing. Please run <a href='setup-database.php'>setup-database.php</a></p>";
                    $all_good = false;
                }
                
                if ($all_good) {
                    echo "<p class='success'>🎉 <strong>Setup Complete!</strong> Your BALLERY.IN website is ready to use.</p>";
                    echo "<ul>";
                    echo "<li>✅ Visit your website: <a href='" . (isset($wp_status['site_url']) ? $wp_status['site_url'] : 'http://localhost/ballery') . "' target='_blank'>View Site</a></li>";
                    echo "<li>✅ Access admin panel: <a href='" . (isset($wp_status['admin_url']) ? $wp_status['admin_url'] : 'http://localhost/ballery/wp-admin') . "' target='_blank'>WordPress Admin</a></li>";
                    echo "<li>✅ Test booking system: <a href='" . (isset($wp_status['site_url']) ? $wp_status['site_url'] : 'http://localhost/ballery') . "/book-consultation' target='_blank'>Book Consultation</a></li>";
                    echo "</ul>";
                    echo "<p><strong>🔒 Security:</strong> Remember to delete setup files (setup-database.php, wordpress-setup.php, verify-setup.php) after testing.</p>";
                }
                ?>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
