<?php
/**
 * Database Setup Script for BALLERY.IN Booking System
 * Run this file once after WordPress installation to create booking tables
 * 
 * @package BALLERY.IN
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // If not in WordPress context, define basic constants for standalone execution
    define('ABSPATH', dirname(__FILE__) . '/');
    
    // Include WordPress configuration
    if (file_exists(ABSPATH . 'wp-config.php')) {
        require_once(ABSPATH . 'wp-config.php');
    } else {
        die('WordPress not found. Please ensure this file is in your WordPress root directory.');
    }
}

global $wpdb;

/**
 * Create Bookings Table
 */
function ballery_create_bookings_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'ballery_bookings';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        booking_id varchar(20) NOT NULL,
        service_type varchar(50) NOT NULL,
        services text NOT NULL,
        client_name varchar(100) NOT NULL,
        client_email varchar(100) NOT NULL,
        client_phone varchar(20) NOT NULL,
        site_location varchar(200) NOT NULL,
        project_description text NOT NULL,
        preferred_date date NOT NULL,
        preferred_time time NOT NULL,
        status varchar(20) DEFAULT 'pending',
        total_amount decimal(10,2) DEFAULT 0.00,
        booking_date datetime DEFAULT CURRENT_TIMESTAMP,
        confirmed_date datetime NULL,
        notes text NULL,
        consultant_assigned varchar(100) NULL,
        payment_status varchar(20) DEFAULT 'pending',
        payment_id varchar(100) NULL,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY booking_id (booking_id),
        KEY status (status),
        KEY booking_date (booking_date),
        KEY preferred_date (preferred_date)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    return $wpdb->last_error ? false : true;
}

/**
 * Create Booking Files Table
 */
function ballery_create_booking_files_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'ballery_booking_files';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        booking_id varchar(20) NOT NULL,
        file_name varchar(255) NOT NULL,
        file_path varchar(500) NOT NULL,
        file_type varchar(50) NOT NULL,
        file_size int(11) NOT NULL,
        uploaded_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY booking_id (booking_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    return $wpdb->last_error ? false : true;
}

/**
 * Create Service Pricing Table
 */
function ballery_create_service_pricing_table() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'ballery_service_pricing';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        service_type varchar(50) NOT NULL,
        service_name varchar(100) NOT NULL,
        service_slug varchar(100) NOT NULL,
        price decimal(10,2) NOT NULL,
        description text NULL,
        is_active tinyint(1) DEFAULT 1,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY service_slug (service_slug),
        KEY service_type (service_type),
        KEY is_active (is_active)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    return $wpdb->last_error ? false : true;
}

/**
 * Insert Default Service Pricing Data
 */
function ballery_insert_default_services() {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'ballery_service_pricing';
    
    // Check if data already exists
    $existing = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    if ($existing > 0) {
        return true; // Data already exists
    }
    
    $services = [
        // Virtual Services
        ['virtual', 'Drawing Evaluation', 'drawing-evaluation', 1499.00, 'Professional review of architectural drawings and plans'],
        ['virtual', 'Agreement Verification', 'agreement-verification', 999.00, 'Legal and technical review of construction agreements'],
        ['virtual', 'BoQ Preparation', 'boq-preparation', 2499.00, 'Detailed Bill of Quantities preparation'],
        ['virtual', 'Structural Consultation', 'structural-consultation', 1999.00, 'Expert structural engineering advice'],
        ['virtual', 'Material Specification Review', 'material-specification', 1299.00, 'Review and optimization of material specifications'],
        ['virtual', 'Cost Estimation', 'cost-estimation', 1799.00, 'Detailed project cost estimation'],
        
        // Site Visit Services
        ['site-visit', 'Site Marking Check', 'site-marking-check', 2999.00, 'Verification of site marking and layout'],
        ['site-visit', 'Steel Reinforcement Check', 'steel-reinforcement-check', 3499.00, 'Quality check of steel reinforcement work'],
        ['site-visit', 'Concreting Check', 'concreting-check', 4499.00, 'Quality inspection during concreting work'],
        ['site-visit', 'Measurements & Billing Check', 'measurements-billing-check', 2499.00, 'Verification of measurements and billing'],
        ['site-visit', 'Quality Inspection', 'quality-inspection', 3999.00, 'Comprehensive quality inspection'],
        ['site-visit', 'Safety Audit', 'safety-audit', 2799.00, 'Construction site safety audit']
    ];
    
    foreach ($services as $service) {
        $wpdb->insert(
            $table_name,
            [
                'service_type' => $service[0],
                'service_name' => $service[1],
                'service_slug' => $service[2],
                'price' => $service[3],
                'description' => $service[4],
                'is_active' => 1
            ],
            ['%s', '%s', '%s', '%f', '%s', '%d']
        );
    }
    
    return $wpdb->last_error ? false : true;
}

/**
 * Run Database Setup
 */
function ballery_run_database_setup() {
    $results = [];
    
    // Create tables
    $results['bookings_table'] = ballery_create_bookings_table();
    $results['booking_files_table'] = ballery_create_booking_files_table();
    $results['service_pricing_table'] = ballery_create_service_pricing_table();
    
    // Insert default data
    $results['default_services'] = ballery_insert_default_services();
    
    return $results;
}

// If this file is accessed directly, run the setup
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    echo "<h2>BALLERY.IN Database Setup</h2>";
    
    $results = ballery_run_database_setup();
    
    echo "<h3>Setup Results:</h3>";
    echo "<ul>";
    foreach ($results as $task => $success) {
        $status = $success ? "✅ SUCCESS" : "❌ FAILED";
        echo "<li><strong>" . ucwords(str_replace('_', ' ', $task)) . ":</strong> $status</li>";
    }
    echo "</ul>";
    
    if (all_successful($results)) {
        echo "<p style='color: green; font-weight: bold;'>✅ Database setup completed successfully!</p>";
        echo "<p>You can now use the booking system. Consider deleting this file for security.</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ Some setup tasks failed. Please check your database configuration.</p>";
    }
}

function all_successful($results) {
    return !in_array(false, $results, true);
}

?>
