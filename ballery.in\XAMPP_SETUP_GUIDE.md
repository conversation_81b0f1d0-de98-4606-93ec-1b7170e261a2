# BALLERY.IN Local Development Setup with XAMPP

## Phase 1: XAMPP Installation

### Step 1: Download and Install XAMPP
1. Go to https://www.apachefriends.org/download.html
2. Download XAMPP for Windows (latest version with PHP 8.1 or higher)
3. Run the installer as Administrator
4. Install to default location: `C:\xampp`
5. Select components: Apache, MySQL, PHP, phpMyAdmin

### Step 2: Start Services
1. Open XAMPP Control Panel as Administrator
2. Start Apache service (click "Start" button)
3. Start MySQL service (click "Start" button)
4. Verify both services show "Running" status

### Step 3: Test Installation
1. Open browser and go to `http://localhost`
2. You should see XAMPP welcome page
3. Click on phpMyAdmin to verify MySQL is working

## Phase 2: WordPress Installation

### Step 1: Download WordPress
1. Go to https://wordpress.org/download/
2. Download latest WordPress zip file
3. Extract to `C:\xampp\htdocs\ballery` (create ballery folder)

### Step 2: Create Database
1. Open phpMyAdmin: `http://localhost/phpmyadmin`
2. Click "New" to create database
3. Database name: `ballery_db`
4. Collation: `utf8mb4_general_ci`
5. Click "Create"

### Step 3: Configure WordPress
1. Navigate to `C:\xampp\htdocs\ballery`
2. Rename `wp-config-sample.php` to `wp-config.php`
3. Edit wp-config.php with these database settings:
   ```php
   define('DB_NAME', 'ballery_db');
   define('DB_USER', 'root');
   define('DB_PASSWORD', '');
   define('DB_HOST', 'localhost');
   ```

### Step 4: Complete WordPress Installation
1. Open browser: `http://localhost/ballery`
2. Follow WordPress installation wizard
3. Site Title: "BALLERY.IN"
4. Username: `admin` (or your choice)
5. Password: Create strong password
6. Email: Your email address

## Phase 3: Theme Setup

The BALLERY.IN theme files are already prepared. After WordPress installation:

1. Copy theme files to: `C:\xampp\htdocs\ballery\wp-content\themes\ballery`
2. Activate the theme in WordPress admin
3. Create required pages (About, Services, Pricing, Contact, Book Consultation)
4. Configure menus and widgets

## Access URLs

- **Website**: http://localhost/ballery
- **WordPress Admin**: http://localhost/ballery/wp-admin
- **phpMyAdmin**: http://localhost/phpmyadmin
- **XAMPP Control**: Start → XAMPP Control Panel

## Troubleshooting

### Apache Won't Start
- Check if port 80 is in use
- In XAMPP Control Panel, click "Config" → "Apache" → "httpd.conf"
- Change `Listen 80` to `Listen 8080`
- Access site at `http://localhost:8080/ballery`

### MySQL Won't Start
- Check if port 3306 is in use
- Stop any existing MySQL services
- Restart XAMPP as Administrator

### File Permissions
- Ensure XAMPP is run as Administrator
- Check folder permissions in `C:\xampp\htdocs`

## Phase 4: Automated Setup (Recommended)

### Quick Setup Option
1. Copy all BALLERY.IN theme files to: `C:\xampp\htdocs\ballery`
2. Download WordPress and extract to the same directory
3. Open browser: `http://localhost/ballery/wordpress-setup.php`
4. Follow the automated setup instructions
5. Run: `http://localhost/ballery/setup-database.php`
6. Activate BALLERY.IN theme in WordPress admin

### Manual Setup Option
Follow the steps in Phase 2 and Phase 3 above.

## Phase 5: Final Configuration

### Step 1: Theme Activation
1. Login to WordPress admin: `http://localhost/ballery/wp-admin`
2. Go to Appearance → Themes
3. Activate "BALLERY.IN Professional Theme"

### Step 2: Create Pages
The theme requires these pages (create if not auto-created):
- Home (set as front page)
- About
- Services
- Pricing
- Contact
- Book Consultation

### Step 3: Configure Menus
1. Go to Appearance → Menus
2. Create "Primary Menu" with all pages
3. Assign to "Primary Menu" location

### Step 4: Test Booking System
1. Visit: `http://localhost/ballery/book-consultation`
2. Fill out and submit a test booking
3. Check WordPress admin → Bookings to see the submission
4. Verify email notifications are working

## Features Included

### ✅ Complete WordPress Theme
- Responsive design with mobile-first approach
- Professional blue/gray color palette with orange accent
- Custom post types for testimonials, team members, portfolio
- SEO optimized and performance enhanced

### ✅ Integrated Booking System
- Local database storage (no external dependencies)
- Service selection with dynamic pricing
- File upload capability for drawings/documents
- Email notifications for clients and admin
- Admin dashboard for booking management

### ✅ Admin Features
- Booking management dashboard
- Service pricing management
- Status tracking and updates
- Client communication tools

### ✅ Security Features
- CSRF protection with nonces
- Input sanitization and validation
- Secure file uploads
- SQL injection prevention

## Testing Checklist

- [ ] Website loads at `http://localhost/ballery`
- [ ] All pages are accessible and display correctly
- [ ] Navigation menu works on desktop and mobile
- [ ] Booking form submits successfully
- [ ] Admin can view bookings in WordPress dashboard
- [ ] Email notifications are sent
- [ ] Mobile responsiveness works
- [ ] Contact forms work
- [ ] All buttons and links function properly

## Maintenance

### Regular Tasks
1. Backup database regularly
2. Update service pricing as needed
3. Monitor booking submissions
4. Respond to client inquiries promptly

### Security
1. Keep WordPress updated
2. Use strong admin passwords
3. Regular security scans
4. Monitor for suspicious activity

## Support

For technical issues:
1. Check XAMPP error logs
2. Enable WordPress debug mode
3. Check browser console for JavaScript errors
4. Verify database connections

## Next Steps

After completing the setup:
1. Customize content and images
2. Set up email SMTP for better deliverability
3. Configure backup solutions
4. Plan for production deployment
5. Set up analytics and monitoring
