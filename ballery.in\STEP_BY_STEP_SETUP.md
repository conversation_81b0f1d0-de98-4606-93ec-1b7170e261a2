# 🚀 BALLERY.IN Local Setup - Step by Step Guide

## Current Status: XAMPP Not Installed

I've detected that XAMPP is not installed on your system. Follow these steps to get your BALLERY.IN website running locally:

## Step 1: Install XAMPP (5 minutes)

### Download XAMPP
1. **XAMPP download page is now open in your browser**
2. **Download XAMPP for Windows** (latest version with PHP 8.1+)
3. **File size**: ~150MB, download time depends on your internet speed

### Install XAMPP
1. **Run the downloaded installer as Administrator**
2. **Installation path**: Use default `C:\xampp` (IMPORTANT: Don't change this)
3. **Components to install**: Select at least:
   - ✅ Apache
   - ✅ MySQL  
   - ✅ PHP
   - ✅ phpMyAdmin
4. **Click Install** and wait for completion (~2-3 minutes)

## Step 2: Start XAMPP Services (2 minutes)

### Launch XAMPP Control Panel
1. **Open XAMPP Control Panel** (should auto-start after installation)
2. **Or manually**: Go to `C:\xampp\xampp-control.exe`
3. **Run as Administrator** if prompted

### Start Required Services
1. **Click "Start" next to Apache** - Wait for green "Running" status
2. **Click "Start" next to MySQL** - Wait for green "Running" status
3. **Both should show green "Running" status**

### Verify Services
- **Apache**: Should show "Running" on port 80
- **MySQL**: Should show "Running" on port 3306
- **Test**: Open browser and go to `http://localhost` - you should see XAMPP welcome page

## Step 3: Setup WordPress & BALLERY.IN Theme (5 minutes)

### Automatic Setup (Recommended)
1. **Run the PowerShell script again**:
   ```
   Right-click on Start-BalleryLocal.ps1 → Run with PowerShell
   ```
2. **The script will now**:
   - ✅ Detect XAMPP is running
   - ✅ Download WordPress automatically
   - ✅ Copy BALLERY.IN theme files
   - ✅ Open setup pages in browser

### Manual Setup (If automatic fails)
1. **Download WordPress**:
   - Go to https://wordpress.org/download/
   - Download latest WordPress ZIP
   - Extract to `C:\xampp\htdocs\ballery\`

2. **Copy Theme Files**:
   - Copy all files from your current `ballery.in` folder
   - Paste into `C:\xampp\htdocs\ballery\`

## Step 4: Configure WordPress (3 minutes)

### Database Setup
1. **Open phpMyAdmin**: http://localhost/phpmyadmin
2. **Create database**:
   - Click "New" on left sidebar
   - Database name: `ballery_db`
   - Collation: `utf8mb4_general_ci`
   - Click "Create"

### WordPress Installation
1. **Open**: http://localhost/ballery/wordpress-setup.php
2. **Or manually**: http://localhost/ballery/wp-admin/install.php
3. **Fill in details**:
   - **Site Title**: BALLERY.IN
   - **Username**: admin
   - **Password**: BalleryAdmin2024! (or your choice)
   - **Email**: <EMAIL>
4. **Click "Install WordPress"**

## Step 5: Setup Booking System (2 minutes)

### Initialize Database Tables
1. **Open**: http://localhost/ballery/setup-database.php
2. **Wait for "Setup Complete" message**
3. **Verify**: All tables should show "✅ SUCCESS"

### Activate Theme
1. **Login to WordPress Admin**: http://localhost/ballery/wp-admin
2. **Go to**: Appearance → Themes
3. **Activate**: "BALLERY.IN Professional Theme"

## Step 6: Verify Everything Works (2 minutes)

### Run Verification
1. **Open**: http://localhost/ballery/verify-setup.php
2. **Check all items show**: ✅ SUCCESS or ✅ Found
3. **Any red ❌ items need attention**

### Test Your Site
1. **Website**: http://localhost/ballery
2. **Test booking form**: http://localhost/ballery/book-consultation
3. **WordPress admin**: http://localhost/ballery/wp-admin

## 🎉 Success! Your Site Should Now Be Running

### Access URLs
- **🌐 Website**: http://localhost/ballery
- **⚙️ WordPress Admin**: http://localhost/ballery/wp-admin  
- **🗄️ Database**: http://localhost/phpmyadmin
- **📊 XAMPP Control**: C:\xampp\xampp-control.exe

### Default Login Credentials
- **Username**: admin
- **Password**: BalleryAdmin2024!

## 🔧 Troubleshooting

### XAMPP Issues
- **Port 80 in use**: Change Apache port to 8080 in XAMPP config
- **Services won't start**: Run XAMPP as Administrator
- **Firewall blocking**: Allow Apache and MySQL through Windows Firewall

### WordPress Issues
- **Database connection error**: Check database name and credentials
- **Theme not showing**: Ensure all files copied to correct location
- **Booking system not working**: Run setup-database.php again

### Common Solutions
1. **Restart XAMPP services** (Stop → Start)
2. **Clear browser cache** (Ctrl+F5)
3. **Check file permissions** (Run as Administrator)
4. **Verify file paths** (C:\xampp\htdocs\ballery\)

## 📞 Need Help?

If you encounter any issues:
1. **Check the verify-setup.php page** for specific error details
2. **Look at XAMPP error logs** in C:\xampp\apache\logs\
3. **Ensure all files are in the correct location**
4. **Try restarting XAMPP services**

## 🚀 Next Steps After Setup

1. **Test all functionality** thoroughly
2. **Customize content and images**
3. **Configure email settings** for contact forms
4. **Add your actual business information**
5. **Test the booking system** with sample data

---

**Total Setup Time: ~15-20 minutes**

**Status**: Follow Step 1 first (Install XAMPP), then run the PowerShell script again!
