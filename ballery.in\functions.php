<?php
/**
 * BUILDER BALLERY Theme Functions
 *
 * @package BUILDER BALLERY
 * @version 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function ballery_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('responsive-embeds');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'ballery'),
        'footer' => __('Footer Menu', 'ballery'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'ballery_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function ballery_scripts() {
    // Main stylesheet
    wp_enqueue_style('ballery-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Google Fonts (already loaded in header.php, but keeping for fallback)
    wp_enqueue_style('ballery-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap', array(), null);
    
    // Main JavaScript (functionality is in footer.php for now)
    wp_enqueue_script('ballery-main', get_template_directory_uri() . '/assets/js/main.js', array(), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('ballery-main', 'ballery_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ballery_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'ballery_scripts');

/**
 * Custom Post Types
 */
function ballery_custom_post_types() {
    // Testimonials
    register_post_type('testimonial', array(
        'labels' => array(
            'name' => 'Testimonials',
            'singular_name' => 'Testimonial',
            'add_new' => 'Add New Testimonial',
            'add_new_item' => 'Add New Testimonial',
            'edit_item' => 'Edit Testimonial',
            'new_item' => 'New Testimonial',
            'view_item' => 'View Testimonial',
            'search_items' => 'Search Testimonials',
            'not_found' => 'No testimonials found',
            'not_found_in_trash' => 'No testimonials found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-format-quote',
    ));
    
    // Team Members
    register_post_type('team_member', array(
        'labels' => array(
            'name' => 'Team Members',
            'singular_name' => 'Team Member',
            'add_new' => 'Add New Team Member',
            'add_new_item' => 'Add New Team Member',
            'edit_item' => 'Edit Team Member',
            'new_item' => 'New Team Member',
            'view_item' => 'View Team Member',
            'search_items' => 'Search Team Members',
            'not_found' => 'No team members found',
            'not_found_in_trash' => 'No team members found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-groups',
    ));
    
    // Portfolio Projects
    register_post_type('portfolio', array(
        'labels' => array(
            'name' => 'Portfolio',
            'singular_name' => 'Project',
            'add_new' => 'Add New Project',
            'add_new_item' => 'Add New Project',
            'edit_item' => 'Edit Project',
            'new_item' => 'New Project',
            'view_item' => 'View Project',
            'search_items' => 'Search Projects',
            'not_found' => 'No projects found',
            'not_found_in_trash' => 'No projects found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-portfolio',
    ));
}
add_action('init', 'ballery_custom_post_types');

/**
 * Custom Meta Fields
 */
function ballery_add_meta_boxes() {
    // Testimonial meta
    add_meta_box(
        'testimonial_details',
        'Testimonial Details',
        'ballery_testimonial_meta_callback',
        'testimonial',
        'normal',
        'high'
    );
    
    // Team member meta
    add_meta_box(
        'team_member_details',
        'Team Member Details',
        'ballery_team_member_meta_callback',
        'team_member',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'ballery_add_meta_boxes');

function ballery_testimonial_meta_callback($post) {
    wp_nonce_field('ballery_testimonial_meta', 'ballery_testimonial_nonce');
    
    $client_name = get_post_meta($post->ID, '_client_name', true);
    $client_location = get_post_meta($post->ID, '_client_location', true);
    $rating = get_post_meta($post->ID, '_rating', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="client_name">Client Name</label></th>';
    echo '<td><input type="text" id="client_name" name="client_name" value="' . esc_attr($client_name) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="client_location">Client Location</label></th>';
    echo '<td><input type="text" id="client_location" name="client_location" value="' . esc_attr($client_location) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="rating">Rating (1-5)</label></th>';
    echo '<td><select id="rating" name="rating">';
    for ($i = 1; $i <= 5; $i++) {
        echo '<option value="' . $i . '"' . selected($rating, $i, false) . '>' . $i . ' Star' . ($i > 1 ? 's' : '') . '</option>';
    }
    echo '</select></td></tr>';
    echo '</table>';
}

function ballery_team_member_meta_callback($post) {
    wp_nonce_field('ballery_team_member_meta', 'ballery_team_member_nonce');
    
    $position = get_post_meta($post->ID, '_position', true);
    $experience = get_post_meta($post->ID, '_experience', true);
    $qualifications = get_post_meta($post->ID, '_qualifications', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="position">Position</label></th>';
    echo '<td><input type="text" id="position" name="position" value="' . esc_attr($position) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="experience">Years of Experience</label></th>';
    echo '<td><input type="number" id="experience" name="experience" value="' . esc_attr($experience) . '" class="small-text" /></td></tr>';
    echo '<tr><th><label for="qualifications">Qualifications</label></th>';
    echo '<td><textarea id="qualifications" name="qualifications" rows="3" class="large-text">' . esc_textarea($qualifications) . '</textarea></td></tr>';
    echo '</table>';
}

/**
 * Save Meta Fields
 */
function ballery_save_meta_fields($post_id) {
    // Testimonial meta
    if (isset($_POST['ballery_testimonial_nonce']) && wp_verify_nonce($_POST['ballery_testimonial_nonce'], 'ballery_testimonial_meta')) {
        if (isset($_POST['client_name'])) {
            update_post_meta($post_id, '_client_name', sanitize_text_field($_POST['client_name']));
        }
        if (isset($_POST['client_location'])) {
            update_post_meta($post_id, '_client_location', sanitize_text_field($_POST['client_location']));
        }
        if (isset($_POST['rating'])) {
            update_post_meta($post_id, '_rating', intval($_POST['rating']));
        }
    }
    
    // Team member meta
    if (isset($_POST['ballery_team_member_nonce']) && wp_verify_nonce($_POST['ballery_team_member_nonce'], 'ballery_team_member_meta')) {
        if (isset($_POST['position'])) {
            update_post_meta($post_id, '_position', sanitize_text_field($_POST['position']));
        }
        if (isset($_POST['experience'])) {
            update_post_meta($post_id, '_experience', intval($_POST['experience']));
        }
        if (isset($_POST['qualifications'])) {
            update_post_meta($post_id, '_qualifications', sanitize_textarea_field($_POST['qualifications']));
        }
    }
}
add_action('save_post', 'ballery_save_meta_fields');

/**
 * AJAX Handler for Contact Forms
 */
function ballery_handle_contact_form() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'ballery_nonce')) {
        wp_die('Security check failed');
    }

    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $message = sanitize_textarea_field($_POST['message']);

    // Send email notification
    $to = get_option('admin_email');
    $subject = 'New Contact Form Submission - BALLERY.IN';
    $body = "Name: $name\nEmail: $email\nPhone: $phone\nMessage: $message";
    $headers = array('Content-Type: text/html; charset=UTF-8');

    if (wp_mail($to, $subject, $body, $headers)) {
        wp_send_json_success('Message sent successfully!');
    } else {
        wp_send_json_error('Failed to send message. Please try again.');
    }
}
add_action('wp_ajax_ballery_contact_form', 'ballery_handle_contact_form');
add_action('wp_ajax_nopriv_ballery_contact_form', 'ballery_handle_contact_form');

/**
 * AJAX Handler for Booking System
 */
function ballery_handle_booking_form() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'ballery_nonce')) {
        wp_die('Security check failed');
    }

    global $wpdb;

    // Sanitize input data
    $service_type = sanitize_text_field($_POST['service_type']);
    $services = isset($_POST['services']) ? array_map('sanitize_text_field', $_POST['services']) : [];
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $location = sanitize_text_field($_POST['location']);
    $description = sanitize_textarea_field($_POST['description']);
    $preferred_date = sanitize_text_field($_POST['preferred_date']);
    $preferred_time = sanitize_text_field($_POST['preferred_time']);

    // Validate required fields
    if (empty($service_type) || empty($services) || empty($name) || empty($email) || empty($phone) || empty($location) || empty($description) || empty($preferred_date) || empty($preferred_time)) {
        wp_send_json_error('Please fill in all required fields.');
        return;
    }

    // Generate unique booking ID
    $booking_id = 'BLY' . date('Ymd') . sprintf('%04d', wp_rand(1000, 9999));

    // Calculate total amount based on selected services
    $total_amount = ballery_calculate_booking_amount($service_type, $services);

    // Insert booking into database
    $table_name = $wpdb->prefix . 'ballery_bookings';

    $result = $wpdb->insert(
        $table_name,
        [
            'booking_id' => $booking_id,
            'service_type' => $service_type,
            'services' => json_encode($services),
            'client_name' => $name,
            'client_email' => $email,
            'client_phone' => $phone,
            'site_location' => $location,
            'project_description' => $description,
            'preferred_date' => $preferred_date,
            'preferred_time' => $preferred_time,
            'total_amount' => $total_amount,
            'status' => 'pending'
        ],
        [
            '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%f', '%s'
        ]
    );

    if ($result === false) {
        wp_send_json_error('Failed to save booking. Please try again.');
        return;
    }

    // Send confirmation emails
    ballery_send_booking_confirmation_emails($booking_id, $name, $email, $phone, $service_type, $services, $preferred_date, $preferred_time, $total_amount);

    wp_send_json_success([
        'message' => 'Booking request submitted successfully!',
        'booking_id' => $booking_id,
        'total_amount' => $total_amount
    ]);
}
add_action('wp_ajax_ballery_booking_form', 'ballery_handle_booking_form');
add_action('wp_ajax_nopriv_ballery_booking_form', 'ballery_handle_booking_form');

/**
 * Calculate booking amount based on services
 */
function ballery_calculate_booking_amount($service_type, $services) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ballery_service_pricing';
    $total = 0;

    foreach ($services as $service_name) {
        $price = $wpdb->get_var($wpdb->prepare(
            "SELECT price FROM $table_name WHERE service_name = %s AND service_type = %s AND is_active = 1",
            $service_name,
            $service_type
        ));

        if ($price) {
            $total += floatval($price);
        }
    }

    return $total;
}

/**
 * Send booking confirmation emails
 */
function ballery_send_booking_confirmation_emails($booking_id, $name, $email, $phone, $service_type, $services, $date, $time, $amount) {
    // Email to client
    $client_subject = 'Booking Confirmation - BALLERY.IN (ID: ' . $booking_id . ')';
    $client_body = "
    <h2>Thank you for choosing BALLERY.IN!</h2>
    <p>Dear $name,</p>
    <p>We have received your consultation booking request. Here are the details:</p>

    <h3>Booking Details:</h3>
    <ul>
        <li><strong>Booking ID:</strong> $booking_id</li>
        <li><strong>Service Type:</strong> " . ucwords(str_replace('-', ' ', $service_type)) . "</li>
        <li><strong>Services:</strong> " . implode(', ', $services) . "</li>
        <li><strong>Preferred Date:</strong> " . date('F j, Y', strtotime($date)) . "</li>
        <li><strong>Preferred Time:</strong> " . date('g:i A', strtotime($time)) . "</li>
        <li><strong>Estimated Amount:</strong> ₹" . number_format($amount, 2) . "</li>
    </ul>

    <h3>What happens next:</h3>
    <ol>
        <li>Our team will review your requirements</li>
        <li>We'll call you within 2 hours to confirm the appointment</li>
        <li>You'll receive a payment link for booking confirmation</li>
        <li>Final confirmation will be sent via email</li>
    </ol>

    <p>For any queries, please contact us at +91-9876543210 or reply to this email.</p>

    <p>Best regards,<br>BALLERY.IN Team</p>
    ";

    // Email to admin
    $admin_subject = 'New Booking Request - ' . $booking_id;
    $admin_body = "
    <h2>New Booking Request Received</h2>

    <h3>Client Details:</h3>
    <ul>
        <li><strong>Name:</strong> $name</li>
        <li><strong>Email:</strong> $email</li>
        <li><strong>Phone:</strong> $phone</li>
    </ul>

    <h3>Booking Details:</h3>
    <ul>
        <li><strong>Booking ID:</strong> $booking_id</li>
        <li><strong>Service Type:</strong> " . ucwords(str_replace('-', ' ', $service_type)) . "</li>
        <li><strong>Services:</strong> " . implode(', ', $services) . "</li>
        <li><strong>Preferred Date:</strong> " . date('F j, Y', strtotime($date)) . "</li>
        <li><strong>Preferred Time:</strong> " . date('g:i A', strtotime($time)) . "</li>
        <li><strong>Estimated Amount:</strong> ₹" . number_format($amount, 2) . "</li>
    </ul>

    <p><strong>Action Required:</strong> Please contact the client within 2 hours to confirm the appointment.</p>
    ";

    $headers = array('Content-Type: text/html; charset=UTF-8');

    // Send emails
    wp_mail($email, $client_subject, $client_body, $headers);
    wp_mail(get_option('admin_email'), $admin_subject, $admin_body, $headers);
}

/**
 * Helper Functions
 */
function ballery_get_testimonials($limit = 3) {
    $testimonials = get_posts(array(
        'post_type' => 'testimonial',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'menu_order',
        'order' => 'ASC'
    ));
    
    return $testimonials;
}

function ballery_get_team_members($limit = -1) {
    $team_members = get_posts(array(
        'post_type' => 'team_member',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'menu_order',
        'order' => 'ASC'
    ));
    
    return $team_members;
}

function ballery_get_portfolio_projects($limit = 6) {
    $projects = get_posts(array(
        'post_type' => 'portfolio',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    return $projects;
}

/**
 * Security Enhancements
 */
function ballery_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
    }
}
add_action('send_headers', 'ballery_security_headers');

/**
 * Performance Optimizations
 */
function ballery_optimize_performance() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'ballery_optimize_performance');

/**
 * Initialize Booking System Database Tables
 */
function ballery_init_booking_system() {
    // Check if tables exist, if not create them
    global $wpdb;

    $bookings_table = $wpdb->prefix . 'ballery_bookings';

    // Check if bookings table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '$bookings_table'") != $bookings_table) {
        // Include the database setup file
        include_once(get_template_directory() . '/setup-database.php');
        ballery_run_database_setup();
    }
}
add_action('after_setup_theme', 'ballery_init_booking_system');

/**
 * Admin Menu for Booking Management
 */
function ballery_add_admin_menu() {
    add_menu_page(
        'BALLERY Bookings',
        'Bookings',
        'manage_options',
        'ballery-bookings',
        'ballery_bookings_page',
        'dashicons-calendar-alt',
        30
    );

    add_submenu_page(
        'ballery-bookings',
        'All Bookings',
        'All Bookings',
        'manage_options',
        'ballery-bookings',
        'ballery_bookings_page'
    );

    add_submenu_page(
        'ballery-bookings',
        'Service Pricing',
        'Service Pricing',
        'manage_options',
        'ballery-pricing',
        'ballery_pricing_page'
    );
}
add_action('admin_menu', 'ballery_add_admin_menu');

/**
 * Bookings Admin Page
 */
function ballery_bookings_page() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ballery_bookings';

    // Handle status updates
    if (isset($_POST['update_status']) && isset($_POST['booking_id']) && isset($_POST['new_status'])) {
        $booking_id = sanitize_text_field($_POST['booking_id']);
        $new_status = sanitize_text_field($_POST['new_status']);

        $wpdb->update(
            $table_name,
            ['status' => $new_status],
            ['booking_id' => $booking_id],
            ['%s'],
            ['%s']
        );

        echo '<div class="notice notice-success"><p>Booking status updated successfully!</p></div>';
    }

    // Get all bookings
    $bookings = $wpdb->get_results("SELECT * FROM $table_name ORDER BY booking_date DESC");

    ?>
    <div class="wrap">
        <h1>BALLERY.IN Bookings</h1>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Booking ID</th>
                    <th>Client</th>
                    <th>Service Type</th>
                    <th>Date & Time</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($bookings as $booking): ?>
                <tr>
                    <td><strong><?php echo esc_html($booking->booking_id); ?></strong></td>
                    <td>
                        <?php echo esc_html($booking->client_name); ?><br>
                        <small><?php echo esc_html($booking->client_email); ?></small><br>
                        <small><?php echo esc_html($booking->client_phone); ?></small>
                    </td>
                    <td><?php echo esc_html(ucwords(str_replace('-', ' ', $booking->service_type))); ?></td>
                    <td>
                        <?php echo date('M j, Y', strtotime($booking->preferred_date)); ?><br>
                        <small><?php echo date('g:i A', strtotime($booking->preferred_time)); ?></small>
                    </td>
                    <td>₹<?php echo number_format($booking->total_amount, 2); ?></td>
                    <td>
                        <span class="status-<?php echo $booking->status; ?>">
                            <?php echo ucfirst($booking->status); ?>
                        </span>
                    </td>
                    <td>
                        <form method="post" style="display: inline;">
                            <input type="hidden" name="booking_id" value="<?php echo esc_attr($booking->booking_id); ?>">
                            <select name="new_status">
                                <option value="pending" <?php selected($booking->status, 'pending'); ?>>Pending</option>
                                <option value="confirmed" <?php selected($booking->status, 'confirmed'); ?>>Confirmed</option>
                                <option value="completed" <?php selected($booking->status, 'completed'); ?>>Completed</option>
                                <option value="cancelled" <?php selected($booking->status, 'cancelled'); ?>>Cancelled</option>
                            </select>
                            <input type="submit" name="update_status" value="Update" class="button button-small">
                        </form>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <style>
    .status-pending { color: #f56500; }
    .status-confirmed { color: #00a32a; }
    .status-completed { color: #135e96; }
    .status-cancelled { color: #d63638; }
    </style>
    <?php
}

/**
 * Service Pricing Admin Page
 */
function ballery_pricing_page() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ballery_service_pricing';

    // Handle price updates
    if (isset($_POST['update_pricing'])) {
        foreach ($_POST['prices'] as $id => $price) {
            $wpdb->update(
                $table_name,
                ['price' => floatval($price)],
                ['id' => intval($id)],
                ['%f'],
                ['%d']
            );
        }
        echo '<div class="notice notice-success"><p>Pricing updated successfully!</p></div>';
    }

    // Get all services
    $services = $wpdb->get_results("SELECT * FROM $table_name ORDER BY service_type, service_name");

    ?>
    <div class="wrap">
        <h1>Service Pricing Management</h1>

        <form method="post">
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Service Type</th>
                        <th>Service Name</th>
                        <th>Current Price (₹)</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($services as $service): ?>
                    <tr>
                        <td><?php echo esc_html(ucwords(str_replace('-', ' ', $service->service_type))); ?></td>
                        <td><?php echo esc_html($service->service_name); ?></td>
                        <td>
                            <input type="number" name="prices[<?php echo $service->id; ?>]"
                                   value="<?php echo $service->price; ?>" step="0.01" min="0"
                                   style="width: 100px;">
                        </td>
                        <td><?php echo $service->is_active ? 'Active' : 'Inactive'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <p class="submit">
                <input type="submit" name="update_pricing" class="button-primary" value="Update Pricing">
            </p>
        </form>
    </div>
    <?php
}

?>
