<?php
/**
 * BUILDER BALLERY Theme Functions
 *
 * @package BUILDER BALLERY
 * @version 1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function ballery_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    add_theme_support('custom-logo');
    add_theme_support('responsive-embeds');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'ballery'),
        'footer' => __('Footer Menu', 'ballery'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'ballery_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function ballery_scripts() {
    // Main stylesheet
    wp_enqueue_style('ballery-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Google Fonts (already loaded in header.php, but keeping for fallback)
    wp_enqueue_style('ballery-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap', array(), null);
    
    // Main JavaScript (functionality is in footer.php for now)
    wp_enqueue_script('ballery-main', get_template_directory_uri() . '/assets/js/main.js', array(), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('ballery-main', 'ballery_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ballery_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'ballery_scripts');

/**
 * Custom Post Types
 */
function ballery_custom_post_types() {
    // Testimonials
    register_post_type('testimonial', array(
        'labels' => array(
            'name' => 'Testimonials',
            'singular_name' => 'Testimonial',
            'add_new' => 'Add New Testimonial',
            'add_new_item' => 'Add New Testimonial',
            'edit_item' => 'Edit Testimonial',
            'new_item' => 'New Testimonial',
            'view_item' => 'View Testimonial',
            'search_items' => 'Search Testimonials',
            'not_found' => 'No testimonials found',
            'not_found_in_trash' => 'No testimonials found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-format-quote',
    ));
    
    // Team Members
    register_post_type('team_member', array(
        'labels' => array(
            'name' => 'Team Members',
            'singular_name' => 'Team Member',
            'add_new' => 'Add New Team Member',
            'add_new_item' => 'Add New Team Member',
            'edit_item' => 'Edit Team Member',
            'new_item' => 'New Team Member',
            'view_item' => 'View Team Member',
            'search_items' => 'Search Team Members',
            'not_found' => 'No team members found',
            'not_found_in_trash' => 'No team members found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-groups',
    ));
    
    // Portfolio Projects
    register_post_type('portfolio', array(
        'labels' => array(
            'name' => 'Portfolio',
            'singular_name' => 'Project',
            'add_new' => 'Add New Project',
            'add_new_item' => 'Add New Project',
            'edit_item' => 'Edit Project',
            'new_item' => 'New Project',
            'view_item' => 'View Project',
            'search_items' => 'Search Projects',
            'not_found' => 'No projects found',
            'not_found_in_trash' => 'No projects found in trash'
        ),
        'public' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'supports' => array('title', 'editor', 'thumbnail'),
        'menu_icon' => 'dashicons-portfolio',
    ));
}
add_action('init', 'ballery_custom_post_types');

/**
 * Custom Meta Fields
 */
function ballery_add_meta_boxes() {
    // Testimonial meta
    add_meta_box(
        'testimonial_details',
        'Testimonial Details',
        'ballery_testimonial_meta_callback',
        'testimonial',
        'normal',
        'high'
    );
    
    // Team member meta
    add_meta_box(
        'team_member_details',
        'Team Member Details',
        'ballery_team_member_meta_callback',
        'team_member',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'ballery_add_meta_boxes');

function ballery_testimonial_meta_callback($post) {
    wp_nonce_field('ballery_testimonial_meta', 'ballery_testimonial_nonce');
    
    $client_name = get_post_meta($post->ID, '_client_name', true);
    $client_location = get_post_meta($post->ID, '_client_location', true);
    $rating = get_post_meta($post->ID, '_rating', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="client_name">Client Name</label></th>';
    echo '<td><input type="text" id="client_name" name="client_name" value="' . esc_attr($client_name) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="client_location">Client Location</label></th>';
    echo '<td><input type="text" id="client_location" name="client_location" value="' . esc_attr($client_location) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="rating">Rating (1-5)</label></th>';
    echo '<td><select id="rating" name="rating">';
    for ($i = 1; $i <= 5; $i++) {
        echo '<option value="' . $i . '"' . selected($rating, $i, false) . '>' . $i . ' Star' . ($i > 1 ? 's' : '') . '</option>';
    }
    echo '</select></td></tr>';
    echo '</table>';
}

function ballery_team_member_meta_callback($post) {
    wp_nonce_field('ballery_team_member_meta', 'ballery_team_member_nonce');
    
    $position = get_post_meta($post->ID, '_position', true);
    $experience = get_post_meta($post->ID, '_experience', true);
    $qualifications = get_post_meta($post->ID, '_qualifications', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="position">Position</label></th>';
    echo '<td><input type="text" id="position" name="position" value="' . esc_attr($position) . '" class="regular-text" /></td></tr>';
    echo '<tr><th><label for="experience">Years of Experience</label></th>';
    echo '<td><input type="number" id="experience" name="experience" value="' . esc_attr($experience) . '" class="small-text" /></td></tr>';
    echo '<tr><th><label for="qualifications">Qualifications</label></th>';
    echo '<td><textarea id="qualifications" name="qualifications" rows="3" class="large-text">' . esc_textarea($qualifications) . '</textarea></td></tr>';
    echo '</table>';
}

/**
 * Save Meta Fields
 */
function ballery_save_meta_fields($post_id) {
    // Testimonial meta
    if (isset($_POST['ballery_testimonial_nonce']) && wp_verify_nonce($_POST['ballery_testimonial_nonce'], 'ballery_testimonial_meta')) {
        if (isset($_POST['client_name'])) {
            update_post_meta($post_id, '_client_name', sanitize_text_field($_POST['client_name']));
        }
        if (isset($_POST['client_location'])) {
            update_post_meta($post_id, '_client_location', sanitize_text_field($_POST['client_location']));
        }
        if (isset($_POST['rating'])) {
            update_post_meta($post_id, '_rating', intval($_POST['rating']));
        }
    }
    
    // Team member meta
    if (isset($_POST['ballery_team_member_nonce']) && wp_verify_nonce($_POST['ballery_team_member_nonce'], 'ballery_team_member_meta')) {
        if (isset($_POST['position'])) {
            update_post_meta($post_id, '_position', sanitize_text_field($_POST['position']));
        }
        if (isset($_POST['experience'])) {
            update_post_meta($post_id, '_experience', intval($_POST['experience']));
        }
        if (isset($_POST['qualifications'])) {
            update_post_meta($post_id, '_qualifications', sanitize_textarea_field($_POST['qualifications']));
        }
    }
}
add_action('save_post', 'ballery_save_meta_fields');

/**
 * AJAX Handler for Contact Forms
 */
function ballery_handle_contact_form() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'ballery_nonce')) {
        wp_die('Security check failed');
    }

    global $wpdb;

    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $subject = isset($_POST['subject']) ? sanitize_text_field($_POST['subject']) : '';
    $message = sanitize_textarea_field($_POST['message']);

    // Generate unique inquiry ID
    $inquiry_id = 'INQ' . date('Ymd') . sprintf('%04d', wp_rand(1000, 9999));

    // Save inquiry to database
    $inquiries_table = $wpdb->prefix . 'ballery_inquiries';

    $result = $wpdb->insert(
        $inquiries_table,
        [
            'inquiry_id' => $inquiry_id,
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'subject' => $subject,
            'message' => $message,
            'inquiry_type' => 'contact_form',
            'status' => 'unread'
        ],
        ['%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s']
    );

    if ($result === false) {
        wp_send_json_error('Failed to save inquiry. Please try again.');
        return;
    }

    // Send email notification to admin
    $to = get_option('admin_email');
    $email_subject = 'New Contact Form Submission - BALLERY.IN (ID: ' . $inquiry_id . ')';
    $body = "
    <h2>New Contact Form Submission</h2>
    <p><strong>Inquiry ID:</strong> $inquiry_id</p>
    <p><strong>Name:</strong> $name</p>
    <p><strong>Email:</strong> $email</p>
    <p><strong>Phone:</strong> $phone</p>
    <p><strong>Subject:</strong> " . ($subject ?: 'General Inquiry') . "</p>
    <p><strong>Message:</strong></p>
    <div style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>$message</div>
    <p><a href='" . admin_url('admin.php?page=ballery-inquiries') . "'>View in Admin Panel</a></p>
    ";
    $headers = array('Content-Type: text/html; charset=UTF-8');

    // Send auto-reply to customer
    $customer_subject = 'Thank you for contacting BALLERY.IN - We\'ll respond soon!';
    $customer_body = "
    <h2>Thank you for contacting BALLERY.IN!</h2>
    <p>Dear $name,</p>
    <p>We have received your inquiry and will respond within 24 hours.</p>
    <p><strong>Your Inquiry ID:</strong> $inquiry_id</p>
    <p><strong>Your Message:</strong></p>
    <div style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>$message</div>
    <p>If you have any urgent questions, please call us at +91-9876543210.</p>
    <p>Best regards,<br>BALLERY.IN Team</p>
    ";

    $admin_sent = wp_mail($to, $email_subject, $body, $headers);
    $customer_sent = wp_mail($email, $customer_subject, $customer_body, $headers);

    if ($admin_sent || $customer_sent) {
        wp_send_json_success([
            'message' => 'Message sent successfully! We\'ll respond within 24 hours.',
            'inquiry_id' => $inquiry_id
        ]);
    } else {
        wp_send_json_error('Message saved but email notification failed. We\'ll still respond to your inquiry.');
    }
}
add_action('wp_ajax_ballery_contact_form', 'ballery_handle_contact_form');
add_action('wp_ajax_nopriv_ballery_contact_form', 'ballery_handle_contact_form');

/**
 * Send inquiry response email
 */
function ballery_send_inquiry_response_email($inquiry, $response) {
    $subject = 'Re: ' . ($inquiry->subject ?: 'Your inquiry to BALLERY.IN');
    $body = "
    <h2>Thank you for contacting BALLERY.IN</h2>
    <p>Dear " . esc_html($inquiry->name) . ",</p>
    <p>Thank you for your inquiry. Here is our response:</p>
    <div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #2c5aa0; margin: 20px 0;'>
        <p>" . nl2br(esc_html($response)) . "</p>
    </div>
    <p>If you need further assistance, please don't hesitate to contact us.</p>
    <p>Best regards,<br>BALLERY.IN Team</p>
    <p><small>Inquiry ID: " . esc_html($inquiry->inquiry_id) . "</small></p>
    ";

    $headers = array('Content-Type: text/html; charset=UTF-8');

    return wp_mail($inquiry->email, $subject, $body, $headers);
}

/**
 * AJAX Handler for Booking System
 */
function ballery_handle_booking_form() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'ballery_nonce')) {
        wp_die('Security check failed');
    }

    global $wpdb;

    // Sanitize input data
    $service_type = sanitize_text_field($_POST['service_type']);
    $services = isset($_POST['services']) ? array_map('sanitize_text_field', $_POST['services']) : [];
    $name = sanitize_text_field($_POST['name']);
    $email = sanitize_email($_POST['email']);
    $phone = sanitize_text_field($_POST['phone']);
    $location = sanitize_text_field($_POST['location']);
    $description = sanitize_textarea_field($_POST['description']);
    $preferred_date = sanitize_text_field($_POST['preferred_date']);
    $preferred_time = sanitize_text_field($_POST['preferred_time']);

    // Validate required fields
    if (empty($service_type) || empty($services) || empty($name) || empty($email) || empty($phone) || empty($location) || empty($description) || empty($preferred_date) || empty($preferred_time)) {
        wp_send_json_error('Please fill in all required fields.');
        return;
    }

    // Generate unique booking ID
    $booking_id = 'BLY' . date('Ymd') . sprintf('%04d', wp_rand(1000, 9999));

    // Calculate total amount based on selected services
    $total_amount = ballery_calculate_booking_amount($service_type, $services);

    // Insert booking into database
    $table_name = $wpdb->prefix . 'ballery_bookings';

    $result = $wpdb->insert(
        $table_name,
        [
            'booking_id' => $booking_id,
            'service_type' => $service_type,
            'services' => json_encode($services),
            'client_name' => $name,
            'client_email' => $email,
            'client_phone' => $phone,
            'site_location' => $location,
            'project_description' => $description,
            'preferred_date' => $preferred_date,
            'preferred_time' => $preferred_time,
            'total_amount' => $total_amount,
            'status' => 'pending'
        ],
        [
            '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%f', '%s'
        ]
    );

    if ($result === false) {
        wp_send_json_error('Failed to save booking. Please try again.');
        return;
    }

    // Send confirmation emails
    ballery_send_booking_confirmation_emails($booking_id, $name, $email, $phone, $service_type, $services, $preferred_date, $preferred_time, $total_amount);

    wp_send_json_success([
        'message' => 'Booking request submitted successfully!',
        'booking_id' => $booking_id,
        'total_amount' => $total_amount
    ]);
}
add_action('wp_ajax_ballery_booking_form', 'ballery_handle_booking_form');
add_action('wp_ajax_nopriv_ballery_booking_form', 'ballery_handle_booking_form');

/**
 * Calculate booking amount based on services
 */
function ballery_calculate_booking_amount($service_type, $services) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ballery_service_pricing';
    $total = 0;

    foreach ($services as $service_name) {
        $price = $wpdb->get_var($wpdb->prepare(
            "SELECT price FROM $table_name WHERE service_name = %s AND service_type = %s AND is_active = 1",
            $service_name,
            $service_type
        ));

        if ($price) {
            $total += floatval($price);
        }
    }

    return $total;
}

/**
 * Send booking confirmation emails
 */
function ballery_send_booking_confirmation_emails($booking_id, $name, $email, $phone, $service_type, $services, $date, $time, $amount) {
    // Email to client
    $client_subject = 'Booking Confirmation - BALLERY.IN (ID: ' . $booking_id . ')';
    $client_body = "
    <h2>Thank you for choosing BALLERY.IN!</h2>
    <p>Dear $name,</p>
    <p>We have received your consultation booking request. Here are the details:</p>

    <h3>Booking Details:</h3>
    <ul>
        <li><strong>Booking ID:</strong> $booking_id</li>
        <li><strong>Service Type:</strong> " . ucwords(str_replace('-', ' ', $service_type)) . "</li>
        <li><strong>Services:</strong> " . implode(', ', $services) . "</li>
        <li><strong>Preferred Date:</strong> " . date('F j, Y', strtotime($date)) . "</li>
        <li><strong>Preferred Time:</strong> " . date('g:i A', strtotime($time)) . "</li>
        <li><strong>Estimated Amount:</strong> ₹" . number_format($amount, 2) . "</li>
    </ul>

    <h3>What happens next:</h3>
    <ol>
        <li>Our team will review your requirements</li>
        <li>We'll call you within 2 hours to confirm the appointment</li>
        <li>You'll receive a payment link for booking confirmation</li>
        <li>Final confirmation will be sent via email</li>
    </ol>

    <p>For any queries, please contact us at +91-9876543210 or reply to this email.</p>

    <p>Best regards,<br>BALLERY.IN Team</p>
    ";

    // Email to admin
    $admin_subject = 'New Booking Request - ' . $booking_id;
    $admin_body = "
    <h2>New Booking Request Received</h2>

    <h3>Client Details:</h3>
    <ul>
        <li><strong>Name:</strong> $name</li>
        <li><strong>Email:</strong> $email</li>
        <li><strong>Phone:</strong> $phone</li>
    </ul>

    <h3>Booking Details:</h3>
    <ul>
        <li><strong>Booking ID:</strong> $booking_id</li>
        <li><strong>Service Type:</strong> " . ucwords(str_replace('-', ' ', $service_type)) . "</li>
        <li><strong>Services:</strong> " . implode(', ', $services) . "</li>
        <li><strong>Preferred Date:</strong> " . date('F j, Y', strtotime($date)) . "</li>
        <li><strong>Preferred Time:</strong> " . date('g:i A', strtotime($time)) . "</li>
        <li><strong>Estimated Amount:</strong> ₹" . number_format($amount, 2) . "</li>
    </ul>

    <p><strong>Action Required:</strong> Please contact the client within 2 hours to confirm the appointment.</p>
    ";

    $headers = array('Content-Type: text/html; charset=UTF-8');

    // Send emails
    wp_mail($email, $client_subject, $client_body, $headers);
    wp_mail(get_option('admin_email'), $admin_subject, $admin_body, $headers);
}

/**
 * Helper Functions
 */
function ballery_get_testimonials($limit = 3) {
    $testimonials = get_posts(array(
        'post_type' => 'testimonial',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'menu_order',
        'order' => 'ASC'
    ));
    
    return $testimonials;
}

function ballery_get_team_members($limit = -1) {
    $team_members = get_posts(array(
        'post_type' => 'team_member',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'menu_order',
        'order' => 'ASC'
    ));
    
    return $team_members;
}

function ballery_get_portfolio_projects($limit = 6) {
    $projects = get_posts(array(
        'post_type' => 'portfolio',
        'posts_per_page' => $limit,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    
    return $projects;
}

/**
 * Security Enhancements
 */
function ballery_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
    }
}
add_action('send_headers', 'ballery_security_headers');

/**
 * Performance Optimizations
 */
function ballery_optimize_performance() {
    // Remove unnecessary WordPress features
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    remove_action('wp_head', 'wp_shortlink_wp_head');
    
    // Disable emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'ballery_optimize_performance');

/**
 * Initialize Booking System Database Tables
 */
function ballery_init_booking_system() {
    // Check if tables exist, if not create them
    global $wpdb;

    $bookings_table = $wpdb->prefix . 'ballery_bookings';

    // Check if bookings table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '$bookings_table'") != $bookings_table) {
        // Include the database setup file
        include_once(get_template_directory() . '/setup-database.php');
        ballery_run_database_setup();
    }
}
add_action('after_setup_theme', 'ballery_init_booking_system');

/**
 * Include additional admin functions
 */
require_once get_template_directory() . '/admin-functions.php';

/**
 * Configure SMTP if enabled
 */
function ballery_configure_smtp($phpmailer) {
    if (get_option('ballery_smtp_enabled')) {
        $phpmailer->isSMTP();
        $phpmailer->Host = get_option('ballery_smtp_host');
        $phpmailer->Port = get_option('ballery_smtp_port', 587);
        $phpmailer->SMTPAuth = true;
        $phpmailer->Username = get_option('ballery_smtp_username');
        $phpmailer->Password = get_option('ballery_smtp_password');

        $encryption = get_option('ballery_smtp_encryption', 'tls');
        if ($encryption !== 'none') {
            $phpmailer->SMTPSecure = $encryption;
        }

        $phpmailer->From = get_option('ballery_from_email', get_option('admin_email'));
        $phpmailer->FromName = get_option('ballery_from_name', 'BALLERY.IN');
    }
}
add_action('phpmailer_init', 'ballery_configure_smtp');

/**
 * Add CC emails to notifications
 */
function ballery_add_cc_emails($args) {
    $cc_emails = get_option('ballery_cc_emails', '');
    if (!empty($cc_emails)) {
        $cc_list = array_filter(array_map('trim', explode("\n", $cc_emails)));
        if (!empty($cc_list)) {
            if (!isset($args['headers'])) {
                $args['headers'] = [];
            }
            foreach ($cc_list as $cc_email) {
                if (is_email($cc_email)) {
                    $args['headers'][] = 'Cc: ' . $cc_email;
                }
            }
        }
    }
    return $args;
}
add_filter('wp_mail', 'ballery_add_cc_emails');

/**
 * Enqueue admin styles
 */
function ballery_admin_styles($hook) {
    // Only load on BALLERY.IN admin pages
    if (strpos($hook, 'ballery-') !== false) {
        wp_enqueue_style(
            'ballery-admin-dashboard',
            get_template_directory_uri() . '/assets/admin-dashboard.css',
            [],
            '1.0.0'
        );
    }
}
add_action('admin_enqueue_scripts', 'ballery_admin_styles');

/**
 * Comprehensive Admin Menu for BALLERY.IN Management
 */
function ballery_add_admin_menu() {
    // Main BALLERY.IN Dashboard
    add_menu_page(
        'BALLERY.IN Dashboard',
        'BALLERY.IN',
        'manage_options',
        'ballery-dashboard',
        'ballery_admin_dashboard',
        'dashicons-admin-home',
        25
    );

    // Dashboard Overview
    add_submenu_page(
        'ballery-dashboard',
        'Dashboard',
        'Dashboard',
        'manage_options',
        'ballery-dashboard',
        'ballery_admin_dashboard'
    );

    // Booking Management
    add_submenu_page(
        'ballery-dashboard',
        'Bookings',
        'Bookings',
        'manage_options',
        'ballery-bookings',
        'ballery_bookings_page'
    );

    // Customer Inquiries
    add_submenu_page(
        'ballery-dashboard',
        'Customer Inquiries',
        'Inquiries',
        'manage_options',
        'ballery-inquiries',
        'ballery_inquiries_page'
    );

    // Service Management
    add_submenu_page(
        'ballery-dashboard',
        'Service Management',
        'Services',
        'manage_options',
        'ballery-services',
        'ballery_services_page'
    );

    // Portfolio/Gallery
    add_submenu_page(
        'ballery-dashboard',
        'Portfolio Gallery',
        'Portfolio',
        'manage_options',
        'ballery-portfolio',
        'ballery_portfolio_page'
    );

    // Email Settings
    add_submenu_page(
        'ballery-dashboard',
        'Email Settings',
        'Email Settings',
        'manage_options',
        'ballery-email',
        'ballery_email_settings_page'
    );

    // Content Management
    add_submenu_page(
        'ballery-dashboard',
        'Content Management',
        'Content',
        'manage_options',
        'ballery-content',
        'ballery_content_page'
    );

    // Reports & Analytics
    add_submenu_page(
        'ballery-dashboard',
        'Reports & Analytics',
        'Reports',
        'manage_options',
        'ballery-reports',
        'ballery_reports_page'
    );
}
add_action('admin_menu', 'ballery_add_admin_menu');

/**
 * Main Admin Dashboard
 */
function ballery_admin_dashboard() {
    global $wpdb;

    // Get dashboard statistics
    $bookings_table = $wpdb->prefix . 'ballery_bookings';
    $inquiries_table = $wpdb->prefix . 'ballery_inquiries';

    $total_bookings = $wpdb->get_var("SELECT COUNT(*) FROM $bookings_table");
    $pending_bookings = $wpdb->get_var("SELECT COUNT(*) FROM $bookings_table WHERE status = 'pending'");
    $confirmed_bookings = $wpdb->get_var("SELECT COUNT(*) FROM $bookings_table WHERE status = 'confirmed'");
    $completed_bookings = $wpdb->get_var("SELECT COUNT(*) FROM $bookings_table WHERE status = 'completed'");

    $total_inquiries = $wpdb->get_var("SELECT COUNT(*) FROM $inquiries_table");
    $unread_inquiries = $wpdb->get_var("SELECT COUNT(*) FROM $inquiries_table WHERE status = 'unread'");

    $total_revenue = $wpdb->get_var("SELECT SUM(total_amount) FROM $bookings_table WHERE status IN ('confirmed', 'completed')");
    $monthly_revenue = $wpdb->get_var("SELECT SUM(total_amount) FROM $bookings_table WHERE status IN ('confirmed', 'completed') AND MONTH(booking_date) = MONTH(CURRENT_DATE()) AND YEAR(booking_date) = YEAR(CURRENT_DATE())");

    // Recent bookings
    $recent_bookings = $wpdb->get_results("SELECT * FROM $bookings_table ORDER BY booking_date DESC LIMIT 5");

    // Recent inquiries
    $recent_inquiries = $wpdb->get_results("SELECT * FROM $inquiries_table ORDER BY created_at DESC LIMIT 5");

    ?>
    <div class="wrap ballery-admin-dashboard">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-admin-home"></span>
            BALLERY.IN Dashboard
        </h1>

        <!-- Dashboard Stats -->
        <div class="ballery-stats-grid">
            <div class="ballery-stat-card bookings">
                <div class="stat-icon">📅</div>
                <div class="stat-content">
                    <h3><?php echo number_format($total_bookings); ?></h3>
                    <p>Total Bookings</p>
                    <small><?php echo $pending_bookings; ?> pending</small>
                </div>
            </div>

            <div class="ballery-stat-card inquiries">
                <div class="stat-icon">💬</div>
                <div class="stat-content">
                    <h3><?php echo number_format($total_inquiries); ?></h3>
                    <p>Customer Inquiries</p>
                    <small><?php echo $unread_inquiries; ?> unread</small>
                </div>
            </div>

            <div class="ballery-stat-card revenue">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <h3>₹<?php echo number_format($total_revenue, 2); ?></h3>
                    <p>Total Revenue</p>
                    <small>₹<?php echo number_format($monthly_revenue, 2); ?> this month</small>
                </div>
            </div>

            <div class="ballery-stat-card completion">
                <div class="stat-icon">✅</div>
                <div class="stat-content">
                    <h3><?php echo number_format($completed_bookings); ?></h3>
                    <p>Completed Projects</p>
                    <small><?php echo $confirmed_bookings; ?> confirmed</small>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="ballery-quick-actions">
            <h2>Quick Actions</h2>
            <div class="action-buttons">
                <a href="<?php echo admin_url('admin.php?page=ballery-bookings'); ?>" class="button button-primary">
                    <span class="dashicons dashicons-calendar-alt"></span> Manage Bookings
                </a>
                <a href="<?php echo admin_url('admin.php?page=ballery-inquiries'); ?>" class="button button-secondary">
                    <span class="dashicons dashicons-email"></span> View Inquiries
                </a>
                <a href="<?php echo admin_url('admin.php?page=ballery-services'); ?>" class="button button-secondary">
                    <span class="dashicons dashicons-admin-tools"></span> Manage Services
                </a>
                <a href="<?php echo admin_url('admin.php?page=ballery-portfolio'); ?>" class="button button-secondary">
                    <span class="dashicons dashicons-format-gallery"></span> Update Portfolio
                </a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="ballery-recent-activity">
            <div class="activity-section">
                <h2>Recent Bookings</h2>
                <div class="activity-list">
                    <?php if ($recent_bookings): ?>
                        <?php foreach ($recent_bookings as $booking): ?>
                        <div class="activity-item booking-item">
                            <div class="item-icon">📅</div>
                            <div class="item-content">
                                <h4><?php echo esc_html($booking->client_name); ?></h4>
                                <p><?php echo esc_html($booking->service_type); ?> - ₹<?php echo number_format($booking->total_amount, 2); ?></p>
                                <small><?php echo date('M j, Y g:i A', strtotime($booking->booking_date)); ?></small>
                            </div>
                            <div class="item-status status-<?php echo $booking->status; ?>">
                                <?php echo ucfirst($booking->status); ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p>No recent bookings found.</p>
                    <?php endif; ?>
                </div>
                <a href="<?php echo admin_url('admin.php?page=ballery-bookings'); ?>" class="view-all-link">View All Bookings →</a>
            </div>

            <div class="activity-section">
                <h2>Recent Inquiries</h2>
                <div class="activity-list">
                    <?php if ($recent_inquiries): ?>
                        <?php foreach ($recent_inquiries as $inquiry): ?>
                        <div class="activity-item inquiry-item">
                            <div class="item-icon">💬</div>
                            <div class="item-content">
                                <h4><?php echo esc_html($inquiry->name); ?></h4>
                                <p><?php echo esc_html(wp_trim_words($inquiry->message, 10)); ?></p>
                                <small><?php echo date('M j, Y g:i A', strtotime($inquiry->created_at)); ?></small>
                            </div>
                            <div class="item-status status-<?php echo $inquiry->status; ?>">
                                <?php echo ucfirst($inquiry->status); ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p>No recent inquiries found.</p>
                    <?php endif; ?>
                </div>
                <a href="<?php echo admin_url('admin.php?page=ballery-inquiries'); ?>" class="view-all-link">View All Inquiries →</a>
            </div>
        </div>
    </div>

    <style>
    .ballery-admin-dashboard {
        background: #f1f1f1;
        margin: -20px -20px 0 -22px;
        padding: 20px;
    }

    .ballery-admin-dashboard h1 {
        background: white;
        padding: 20px;
        margin: 0 0 20px 0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .ballery-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .ballery-stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .stat-content h3 {
        margin: 0;
        font-size: 2rem;
        font-weight: bold;
        color: #2c5aa0;
    }

    .stat-content p {
        margin: 5px 0;
        font-weight: 600;
        color: #333;
    }

    .stat-content small {
        color: #666;
    }

    .ballery-quick-actions {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .action-buttons .button {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .ballery-recent-activity {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .activity-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .item-icon {
        font-size: 1.5rem;
    }

    .item-content {
        flex: 1;
    }

    .item-content h4 {
        margin: 0 0 5px 0;
        font-size: 14px;
        font-weight: 600;
    }

    .item-content p {
        margin: 0 0 5px 0;
        font-size: 13px;
        color: #666;
    }

    .item-content small {
        font-size: 12px;
        color: #999;
    }

    .item-status {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-pending { background: #fff3cd; color: #856404; }
    .status-confirmed { background: #d4edda; color: #155724; }
    .status-completed { background: #cce5ff; color: #004085; }
    .status-cancelled { background: #f8d7da; color: #721c24; }
    .status-unread { background: #fff3cd; color: #856404; }
    .status-read { background: #d4edda; color: #155724; }

    .view-all-link {
        display: inline-block;
        margin-top: 15px;
        color: #2c5aa0;
        text-decoration: none;
        font-weight: 600;
    }

    .view-all-link:hover {
        text-decoration: underline;
    }

    @media (max-width: 768px) {
        .ballery-recent-activity {
            grid-template-columns: 1fr;
        }

        .ballery-stats-grid {
            grid-template-columns: 1fr;
        }
    }
    </style>
    <?php
}

/**
 * Customer Inquiries Management Page
 */
function ballery_inquiries_page() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ballery_inquiries';

    // Handle actions
    if (isset($_POST['action'])) {
        $action = sanitize_text_field($_POST['action']);
        $inquiry_id = sanitize_text_field($_POST['inquiry_id']);

        switch ($action) {
            case 'mark_read':
                $wpdb->update(
                    $table_name,
                    ['status' => 'read'],
                    ['inquiry_id' => $inquiry_id],
                    ['%s'],
                    ['%s']
                );
                break;

            case 'mark_unread':
                $wpdb->update(
                    $table_name,
                    ['status' => 'unread'],
                    ['inquiry_id' => $inquiry_id],
                    ['%s'],
                    ['%s']
                );
                break;

            case 'respond':
                $response = sanitize_textarea_field($_POST['response']);
                $wpdb->update(
                    $table_name,
                    [
                        'response' => $response,
                        'responded_at' => current_time('mysql'),
                        'status' => 'responded'
                    ],
                    ['inquiry_id' => $inquiry_id],
                    ['%s', '%s', '%s'],
                    ['%s']
                );

                // Send response email
                $inquiry = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table_name WHERE inquiry_id = %s", $inquiry_id));
                if ($inquiry) {
                    ballery_send_inquiry_response_email($inquiry, $response);
                }
                break;

            case 'delete':
                $wpdb->delete(
                    $table_name,
                    ['inquiry_id' => $inquiry_id],
                    ['%s']
                );
                break;
        }

        echo '<div class="notice notice-success"><p>Action completed successfully!</p></div>';
    }

    // Handle bulk actions
    if (isset($_POST['bulk_action']) && isset($_POST['inquiry_ids'])) {
        $bulk_action = sanitize_text_field($_POST['bulk_action']);
        $inquiry_ids = array_map('sanitize_text_field', $_POST['inquiry_ids']);

        foreach ($inquiry_ids as $inquiry_id) {
            switch ($bulk_action) {
                case 'mark_read':
                    $wpdb->update(
                        $table_name,
                        ['status' => 'read'],
                        ['inquiry_id' => $inquiry_id],
                        ['%s'],
                        ['%s']
                    );
                    break;

                case 'delete':
                    $wpdb->delete(
                        $table_name,
                        ['inquiry_id' => $inquiry_id],
                        ['%s']
                    );
                    break;
            }
        }

        echo '<div class="notice notice-success"><p>Bulk action completed successfully!</p></div>';
    }

    // Get filter parameters
    $status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
    $search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

    // Build query
    $where_conditions = [];
    $where_values = [];

    if ($status_filter) {
        $where_conditions[] = "status = %s";
        $where_values[] = $status_filter;
    }

    if ($search) {
        $where_conditions[] = "(name LIKE %s OR email LIKE %s OR message LIKE %s)";
        $search_term = '%' . $wpdb->esc_like($search) . '%';
        $where_values[] = $search_term;
        $where_values[] = $search_term;
        $where_values[] = $search_term;
    }

    $where_clause = '';
    if (!empty($where_conditions)) {
        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    }

    // Get inquiries with pagination
    $per_page = 20;
    $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
    $offset = ($current_page - 1) * $per_page;

    $query = "SELECT * FROM $table_name $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d";
    $where_values[] = $per_page;
    $where_values[] = $offset;

    if (!empty($where_values)) {
        $inquiries = $wpdb->get_results($wpdb->prepare($query, $where_values));
        $total_query = "SELECT COUNT(*) FROM $table_name $where_clause";
        array_pop($where_values); // Remove LIMIT
        array_pop($where_values); // Remove OFFSET
        $total_items = $wpdb->get_var(!empty($where_values) ? $wpdb->prepare($total_query, $where_values) : $total_query);
    } else {
        $inquiries = $wpdb->get_results($query);
        $total_items = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    }

    $total_pages = ceil($total_items / $per_page);

    // Get status counts
    $status_counts = $wpdb->get_results("
        SELECT status, COUNT(*) as count
        FROM $table_name
        GROUP BY status
    ", OBJECT_K);

    ?>
    <div class="wrap ballery-inquiries-page">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-email"></span>
            Customer Inquiries
        </h1>

        <!-- Status Filter Tabs -->
        <div class="subsubsub">
            <a href="<?php echo admin_url('admin.php?page=ballery-inquiries'); ?>"
               class="<?php echo empty($status_filter) ? 'current' : ''; ?>">
                All <span class="count">(<?php echo $total_items; ?>)</span>
            </a> |
            <a href="<?php echo admin_url('admin.php?page=ballery-inquiries&status=unread'); ?>"
               class="<?php echo $status_filter === 'unread' ? 'current' : ''; ?>">
                Unread <span class="count">(<?php echo isset($status_counts['unread']) ? $status_counts['unread']->count : 0; ?>)</span>
            </a> |
            <a href="<?php echo admin_url('admin.php?page=ballery-inquiries&status=read'); ?>"
               class="<?php echo $status_filter === 'read' ? 'current' : ''; ?>">
                Read <span class="count">(<?php echo isset($status_counts['read']) ? $status_counts['read']->count : 0; ?>)</span>
            </a> |
            <a href="<?php echo admin_url('admin.php?page=ballery-inquiries&status=responded'); ?>"
               class="<?php echo $status_filter === 'responded' ? 'current' : ''; ?>">
                Responded <span class="count">(<?php echo isset($status_counts['responded']) ? $status_counts['responded']->count : 0; ?>)</span>
            </a>
        </div>

        <!-- Search and Bulk Actions -->
        <div class="tablenav top">
            <div class="alignleft actions bulkactions">
                <select name="bulk_action" id="bulk-action-selector-top">
                    <option value="">Bulk Actions</option>
                    <option value="mark_read">Mark as Read</option>
                    <option value="delete">Delete</option>
                </select>
                <input type="submit" id="doaction" class="button action" value="Apply">
            </div>

            <div class="alignright">
                <form method="get" style="display: inline-block;">
                    <input type="hidden" name="page" value="ballery-inquiries">
                    <?php if ($status_filter): ?>
                        <input type="hidden" name="status" value="<?php echo esc_attr($status_filter); ?>">
                    <?php endif; ?>
                    <input type="search" name="search" value="<?php echo esc_attr($search); ?>" placeholder="Search inquiries...">
                    <input type="submit" class="button" value="Search">
                </form>
            </div>
        </div>

        <!-- Inquiries Table -->
        <form method="post" id="inquiries-form">
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <td class="manage-column column-cb check-column">
                            <input type="checkbox" id="cb-select-all-1">
                        </td>
                        <th>Inquiry ID</th>
                        <th>Customer</th>
                        <th>Subject</th>
                        <th>Message</th>
                        <th>Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($inquiries): ?>
                        <?php foreach ($inquiries as $inquiry): ?>
                        <tr class="inquiry-row <?php echo $inquiry->status === 'unread' ? 'unread' : ''; ?>">
                            <th class="check-column">
                                <input type="checkbox" name="inquiry_ids[]" value="<?php echo esc_attr($inquiry->inquiry_id); ?>">
                            </th>
                            <td><strong><?php echo esc_html($inquiry->inquiry_id); ?></strong></td>
                            <td>
                                <strong><?php echo esc_html($inquiry->name); ?></strong><br>
                                <small><?php echo esc_html($inquiry->email); ?></small><br>
                                <small><?php echo esc_html($inquiry->phone); ?></small>
                            </td>
                            <td><?php echo esc_html($inquiry->subject ?: 'General Inquiry'); ?></td>
                            <td>
                                <div class="message-preview">
                                    <?php echo esc_html(wp_trim_words($inquiry->message, 15)); ?>
                                </div>
                            </td>
                            <td>
                                <?php echo date('M j, Y', strtotime($inquiry->created_at)); ?><br>
                                <small><?php echo date('g:i A', strtotime($inquiry->created_at)); ?></small>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $inquiry->status; ?>">
                                    <?php echo ucfirst($inquiry->status); ?>
                                </span>
                            </td>
                            <td>
                                <button type="button" class="button button-small view-inquiry"
                                        data-inquiry='<?php echo esc_attr(json_encode($inquiry)); ?>'>
                                    View
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 40px;">
                                No inquiries found.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </form>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="tablenav bottom">
            <div class="tablenav-pages">
                <?php
                $page_links = paginate_links([
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'prev_text' => '&laquo;',
                    'next_text' => '&raquo;',
                    'total' => $total_pages,
                    'current' => $current_page
                ]);
                echo $page_links;
                ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Inquiry Detail Modal -->
    <div id="inquiry-modal" class="ballery-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Inquiry Details</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div id="inquiry-details"></div>

                <div class="response-section">
                    <h3>Send Response</h3>
                    <form id="response-form" method="post">
                        <input type="hidden" name="action" value="respond">
                        <input type="hidden" name="inquiry_id" id="response-inquiry-id">
                        <textarea name="response" id="response-text" rows="6" placeholder="Type your response here..." required></textarea>
                        <div class="response-actions">
                            <button type="submit" class="button button-primary">Send Response</button>
                            <button type="button" class="button mark-read-btn">Mark as Read</button>
                            <button type="button" class="button delete-btn" style="color: #d63638;">Delete Inquiry</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <style>
    .ballery-inquiries-page .inquiry-row.unread {
        background-color: #fff3cd;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-unread { background: #fff3cd; color: #856404; }
    .status-read { background: #d4edda; color: #155724; }
    .status-responded { background: #cce5ff; color: #004085; }

    .message-preview {
        max-width: 300px;
        word-wrap: break-word;
    }

    .ballery-modal {
        position: fixed;
        z-index: 100000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 0;
        border-radius: 8px;
        width: 80%;
        max-width: 800px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
    }

    .close {
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .modal-body {
        padding: 20px;
    }

    .response-section {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #dee2e6;
    }

    .response-section textarea {
        width: 100%;
        margin-bottom: 15px;
    }

    .response-actions {
        display: flex;
        gap: 10px;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // View inquiry modal
        $('.view-inquiry').on('click', function() {
            var inquiry = $(this).data('inquiry');
            var html = '<div class="inquiry-info">';
            html += '<h3>Customer Information</h3>';
            html += '<p><strong>Name:</strong> ' + inquiry.name + '</p>';
            html += '<p><strong>Email:</strong> ' + inquiry.email + '</p>';
            html += '<p><strong>Phone:</strong> ' + inquiry.phone + '</p>';
            html += '<p><strong>Subject:</strong> ' + (inquiry.subject || 'General Inquiry') + '</p>';
            html += '<p><strong>Date:</strong> ' + new Date(inquiry.created_at).toLocaleString() + '</p>';
            html += '<h3>Message</h3>';
            html += '<div style="background: #f8f9fa; padding: 15px; border-radius: 4px;">' + inquiry.message + '</div>';

            if (inquiry.response) {
                html += '<h3>Previous Response</h3>';
                html += '<div style="background: #e7f3ff; padding: 15px; border-radius: 4px;">' + inquiry.response + '</div>';
                html += '<p><small>Responded on: ' + new Date(inquiry.responded_at).toLocaleString() + '</small></p>';
            }

            html += '</div>';

            $('#inquiry-details').html(html);
            $('#response-inquiry-id').val(inquiry.inquiry_id);
            $('#inquiry-modal').show();
        });

        // Close modal
        $('.close, .ballery-modal').on('click', function(e) {
            if (e.target === this) {
                $('#inquiry-modal').hide();
            }
        });

        // Mark as read
        $('.mark-read-btn').on('click', function() {
            var inquiryId = $('#response-inquiry-id').val();
            var form = $('<form method="post"><input type="hidden" name="action" value="mark_read"><input type="hidden" name="inquiry_id" value="' + inquiryId + '"></form>');
            $('body').append(form);
            form.submit();
        });

        // Delete inquiry
        $('.delete-btn').on('click', function() {
            if (confirm('Are you sure you want to delete this inquiry?')) {
                var inquiryId = $('#response-inquiry-id').val();
                var form = $('<form method="post"><input type="hidden" name="action" value="delete"><input type="hidden" name="inquiry_id" value="' + inquiryId + '"></form>');
                $('body').append(form);
                form.submit();
            }
        });

        // Bulk actions
        $('#doaction').on('click', function(e) {
            e.preventDefault();
            var action = $('#bulk-action-selector-top').val();
            if (!action) return;

            var checkedBoxes = $('input[name="inquiry_ids[]"]:checked');
            if (checkedBoxes.length === 0) {
                alert('Please select at least one inquiry.');
                return;
            }

            if (action === 'delete' && !confirm('Are you sure you want to delete the selected inquiries?')) {
                return;
            }

            var form = $('<form method="post"></form>');
            form.append('<input type="hidden" name="bulk_action" value="' + action + '">');
            checkedBoxes.each(function() {
                form.append('<input type="hidden" name="inquiry_ids[]" value="' + $(this).val() + '">');
            });
            $('body').append(form);
            form.submit();
        });

        // Select all checkbox
        $('#cb-select-all-1').on('change', function() {
            $('input[name="inquiry_ids[]"]').prop('checked', this.checked);
        });
    });
    </script>
    <?php
}

/**
 * Bookings Admin Page
 */
function ballery_bookings_page() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ballery_bookings';

    // Handle status updates
    if (isset($_POST['update_status']) && isset($_POST['booking_id']) && isset($_POST['new_status'])) {
        $booking_id = sanitize_text_field($_POST['booking_id']);
        $new_status = sanitize_text_field($_POST['new_status']);

        $wpdb->update(
            $table_name,
            ['status' => $new_status],
            ['booking_id' => $booking_id],
            ['%s'],
            ['%s']
        );

        echo '<div class="notice notice-success"><p>Booking status updated successfully!</p></div>';
    }

    // Get all bookings
    $bookings = $wpdb->get_results("SELECT * FROM $table_name ORDER BY booking_date DESC");

    ?>
    <div class="wrap">
        <h1>BALLERY.IN Bookings</h1>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Booking ID</th>
                    <th>Client</th>
                    <th>Service Type</th>
                    <th>Date & Time</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($bookings as $booking): ?>
                <tr>
                    <td><strong><?php echo esc_html($booking->booking_id); ?></strong></td>
                    <td>
                        <?php echo esc_html($booking->client_name); ?><br>
                        <small><?php echo esc_html($booking->client_email); ?></small><br>
                        <small><?php echo esc_html($booking->client_phone); ?></small>
                    </td>
                    <td><?php echo esc_html(ucwords(str_replace('-', ' ', $booking->service_type))); ?></td>
                    <td>
                        <?php echo date('M j, Y', strtotime($booking->preferred_date)); ?><br>
                        <small><?php echo date('g:i A', strtotime($booking->preferred_time)); ?></small>
                    </td>
                    <td>₹<?php echo number_format($booking->total_amount, 2); ?></td>
                    <td>
                        <span class="status-<?php echo $booking->status; ?>">
                            <?php echo ucfirst($booking->status); ?>
                        </span>
                    </td>
                    <td>
                        <form method="post" style="display: inline;">
                            <input type="hidden" name="booking_id" value="<?php echo esc_attr($booking->booking_id); ?>">
                            <select name="new_status">
                                <option value="pending" <?php selected($booking->status, 'pending'); ?>>Pending</option>
                                <option value="confirmed" <?php selected($booking->status, 'confirmed'); ?>>Confirmed</option>
                                <option value="completed" <?php selected($booking->status, 'completed'); ?>>Completed</option>
                                <option value="cancelled" <?php selected($booking->status, 'cancelled'); ?>>Cancelled</option>
                            </select>
                            <input type="submit" name="update_status" value="Update" class="button button-small">
                        </form>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <style>
    .status-pending { color: #f56500; }
    .status-confirmed { color: #00a32a; }
    .status-completed { color: #135e96; }
    .status-cancelled { color: #d63638; }
    </style>
    <?php
}

/**
 * Service Pricing Admin Page
 */
function ballery_pricing_page() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ballery_service_pricing';

    // Handle price updates
    if (isset($_POST['update_pricing'])) {
        foreach ($_POST['prices'] as $id => $price) {
            $wpdb->update(
                $table_name,
                ['price' => floatval($price)],
                ['id' => intval($id)],
                ['%f'],
                ['%d']
            );
        }
        echo '<div class="notice notice-success"><p>Pricing updated successfully!</p></div>';
    }

    // Get all services
    $services = $wpdb->get_results("SELECT * FROM $table_name ORDER BY service_type, service_name");

    ?>
    <div class="wrap">
        <h1>Service Pricing Management</h1>

        <form method="post">
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Service Type</th>
                        <th>Service Name</th>
                        <th>Current Price (₹)</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($services as $service): ?>
                    <tr>
                        <td><?php echo esc_html(ucwords(str_replace('-', ' ', $service->service_type))); ?></td>
                        <td><?php echo esc_html($service->service_name); ?></td>
                        <td>
                            <input type="number" name="prices[<?php echo $service->id; ?>]"
                                   value="<?php echo $service->price; ?>" step="0.01" min="0"
                                   style="width: 100px;">
                        </td>
                        <td><?php echo $service->is_active ? 'Active' : 'Inactive'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <p class="submit">
                <input type="submit" name="update_pricing" class="button-primary" value="Update Pricing">
            </p>
        </form>
    </div>
    <?php
}

/**
 * Service Management Page
 */
function ballery_services_page() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'ballery_service_pricing';

    // Handle form submissions
    if (isset($_POST['action'])) {
        $action = sanitize_text_field($_POST['action']);

        switch ($action) {
            case 'add_service':
                $service_type = sanitize_text_field($_POST['service_type']);
                $service_name = sanitize_text_field($_POST['service_name']);
                $service_slug = sanitize_title($_POST['service_name']);
                $price = floatval($_POST['price']);
                $description = sanitize_textarea_field($_POST['description']);

                $result = $wpdb->insert(
                    $table_name,
                    [
                        'service_type' => $service_type,
                        'service_name' => $service_name,
                        'service_slug' => $service_slug,
                        'price' => $price,
                        'description' => $description,
                        'is_active' => 1
                    ],
                    ['%s', '%s', '%s', '%f', '%s', '%d']
                );

                if ($result) {
                    echo '<div class="notice notice-success"><p>Service added successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to add service. Please try again.</p></div>';
                }
                break;

            case 'update_service':
                $service_id = intval($_POST['service_id']);
                $service_name = sanitize_text_field($_POST['service_name']);
                $price = floatval($_POST['price']);
                $description = sanitize_textarea_field($_POST['description']);
                $is_active = isset($_POST['is_active']) ? 1 : 0;

                $result = $wpdb->update(
                    $table_name,
                    [
                        'service_name' => $service_name,
                        'price' => $price,
                        'description' => $description,
                        'is_active' => $is_active
                    ],
                    ['id' => $service_id],
                    ['%s', '%f', '%s', '%d'],
                    ['%d']
                );

                if ($result !== false) {
                    echo '<div class="notice notice-success"><p>Service updated successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to update service. Please try again.</p></div>';
                }
                break;

            case 'delete_service':
                $service_id = intval($_POST['service_id']);

                $result = $wpdb->delete(
                    $table_name,
                    ['id' => $service_id],
                    ['%d']
                );

                if ($result) {
                    echo '<div class="notice notice-success"><p>Service deleted successfully!</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>Failed to delete service. Please try again.</p></div>';
                }
                break;
        }
    }

    // Get services grouped by type
    $virtual_services = $wpdb->get_results("SELECT * FROM $table_name WHERE service_type = 'virtual' ORDER BY service_name");
    $site_visit_services = $wpdb->get_results("SELECT * FROM $table_name WHERE service_type = 'site-visit' ORDER BY service_name");

    ?>
    <div class="wrap ballery-services-page">
        <h1 class="wp-heading-inline">
            <span class="dashicons dashicons-admin-tools"></span>
            Service Management
        </h1>

        <a href="#add-service" class="page-title-action" id="add-service-btn">Add New Service</a>

        <!-- Service Categories Tabs -->
        <div class="nav-tab-wrapper">
            <a href="#virtual-services" class="nav-tab nav-tab-active" data-tab="virtual">Virtual Services</a>
            <a href="#site-visit-services" class="nav-tab" data-tab="site-visit">Site Visit Services</a>
        </div>

        <!-- Virtual Services Tab -->
        <div id="virtual-services" class="tab-content active">
            <h2>Virtual Consultation Services</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th>Price (₹)</th>
                        <th>Description</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($virtual_services as $service): ?>
                    <tr>
                        <td><strong><?php echo esc_html($service->service_name); ?></strong></td>
                        <td>₹<?php echo number_format($service->price, 2); ?></td>
                        <td><?php echo esc_html(wp_trim_words($service->description, 10)); ?></td>
                        <td>
                            <span class="status-badge <?php echo $service->is_active ? 'active' : 'inactive'; ?>">
                                <?php echo $service->is_active ? 'Active' : 'Inactive'; ?>
                            </span>
                        </td>
                        <td>
                            <button class="button button-small edit-service"
                                    data-service='<?php echo esc_attr(json_encode($service)); ?>'>
                                Edit
                            </button>
                            <button class="button button-small delete-service"
                                    data-id="<?php echo $service->id; ?>"
                                    data-name="<?php echo esc_attr($service->service_name); ?>">
                                Delete
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Site Visit Services Tab -->
        <div id="site-visit-services" class="tab-content">
            <h2>Site Visit Services</h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th>Price (₹)</th>
                        <th>Description</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($site_visit_services as $service): ?>
                    <tr>
                        <td><strong><?php echo esc_html($service->service_name); ?></strong></td>
                        <td>₹<?php echo number_format($service->price, 2); ?></td>
                        <td><?php echo esc_html(wp_trim_words($service->description, 10)); ?></td>
                        <td>
                            <span class="status-badge <?php echo $service->is_active ? 'active' : 'inactive'; ?>">
                                <?php echo $service->is_active ? 'Active' : 'Inactive'; ?>
                            </span>
                        </td>
                        <td>
                            <button class="button button-small edit-service"
                                    data-service='<?php echo esc_attr(json_encode($service)); ?>'>
                                Edit
                            </button>
                            <button class="button button-small delete-service"
                                    data-id="<?php echo $service->id; ?>"
                                    data-name="<?php echo esc_attr($service->service_name); ?>">
                                Delete
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add/Edit Service Modal -->
    <div id="service-modal" class="ballery-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Add New Service</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="service-form" method="post">
                    <input type="hidden" name="action" id="form-action" value="add_service">
                    <input type="hidden" name="service_id" id="service-id">

                    <table class="form-table">
                        <tr>
                            <th><label for="service-type">Service Type</label></th>
                            <td>
                                <select name="service_type" id="service-type" required>
                                    <option value="">Select Type</option>
                                    <option value="virtual">Virtual Consultation</option>
                                    <option value="site-visit">Site Visit</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="service-name">Service Name</label></th>
                            <td>
                                <input type="text" name="service_name" id="service-name"
                                       class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="service-price">Price (₹)</label></th>
                            <td>
                                <input type="number" name="price" id="service-price"
                                       step="0.01" min="0" class="regular-text" required>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="service-description">Description</label></th>
                            <td>
                                <textarea name="description" id="service-description"
                                          rows="4" class="large-text"></textarea>
                            </td>
                        </tr>
                        <tr id="active-row" style="display: none;">
                            <th><label for="is-active">Status</label></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="is_active" id="is-active" value="1">
                                    Active
                                </label>
                            </td>
                        </tr>
                    </table>

                    <p class="submit">
                        <input type="submit" class="button-primary" id="submit-btn" value="Add Service">
                        <button type="button" class="button" onclick="jQuery('#service-modal').hide();">Cancel</button>
                    </p>
                </form>
            </div>
        </div>
    </div>

    <style>
    .ballery-services-page .nav-tab-wrapper {
        margin: 20px 0;
    }

    .tab-content {
        display: none;
        padding: 20px 0;
    }

    .tab-content.active {
        display: block;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-badge.active {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
    }

    .ballery-modal {
        position: fixed;
        z-index: 100000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 0;
        border-radius: 8px;
        width: 80%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
    }

    .close {
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .modal-body {
        padding: 20px;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // Tab switching
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            var tab = $(this).data('tab');

            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');

            $('.tab-content').removeClass('active');
            $('#' + tab + '-services').addClass('active');
        });

        // Add service button
        $('#add-service-btn').on('click', function(e) {
            e.preventDefault();
            $('#modal-title').text('Add New Service');
            $('#form-action').val('add_service');
            $('#submit-btn').val('Add Service');
            $('#service-form')[0].reset();
            $('#service-id').val('');
            $('#active-row').hide();
            $('#service-modal').show();
        });

        // Edit service button
        $('.edit-service').on('click', function() {
            var service = $(this).data('service');

            $('#modal-title').text('Edit Service');
            $('#form-action').val('update_service');
            $('#submit-btn').val('Update Service');
            $('#service-id').val(service.id);
            $('#service-type').val(service.service_type);
            $('#service-name').val(service.service_name);
            $('#service-price').val(service.price);
            $('#service-description').val(service.description);
            $('#is-active').prop('checked', service.is_active == 1);
            $('#active-row').show();
            $('#service-modal').show();
        });

        // Delete service button
        $('.delete-service').on('click', function() {
            var id = $(this).data('id');
            var name = $(this).data('name');

            if (confirm('Are you sure you want to delete "' + name + '"?')) {
                var form = $('<form method="post">' +
                    '<input type="hidden" name="action" value="delete_service">' +
                    '<input type="hidden" name="service_id" value="' + id + '">' +
                    '</form>');
                $('body').append(form);
                form.submit();
            }
        });

        // Close modal
        $('.close, .ballery-modal').on('click', function(e) {
            if (e.target === this) {
                $('#service-modal').hide();
            }
        });
    });
    </script>
    <?php
}

?>
