@echo off
title BALLERY.IN Quick Start
color 0A

echo.
echo  ██████╗  █████╗ ██╗     ██╗     ███████╗██████╗ ██╗   ██╗
echo  ██╔══██╗██╔══██╗██║     ██║     ██╔════╝██╔══██╗╚██╗ ██╔╝
echo  ██████╔╝███████║██║     ██║     █████╗  ██████╔╝ ╚████╔╝ 
echo  ██╔══██╗██╔══██║██║     ██║     ██╔══╝  ██╔══██╗  ╚██╔╝  
echo  ██████╔╝██║  ██║███████╗███████╗███████╗██║  ██║   ██║   
echo  ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚══════╝╚═╝  ╚═╝   ╚═╝   
echo.
echo                    Quick Start Launcher
echo  ================================================================
echo.

REM Check if XAMPP is installed
if not exist "C:\xampp\xampp-control.exe" (
    echo  [ERROR] XAMPP not found!
    echo.
    echo  Please install XAMPP first:
    echo  1. Download from: https://www.apachefriends.org/download.html
    echo  2. Install to C:\xampp
    echo  3. Run this script again
    echo.
    echo  Opening download page...
    start https://www.apachefriends.org/download.html
    echo.
    pause
    exit /b 1
)

echo  [INFO] XAMPP found! Starting services...
echo.

REM Start XAMPP Control Panel
start "" "C:\xampp\xampp-control.exe"

REM Wait a moment for XAMPP to start
timeout /t 3 /nobreak >nul

echo  [INFO] XAMPP Control Panel opened
echo  [INFO] Please start Apache and MySQL services
echo.
echo  Press any key when services are running...
pause >nul

REM Check if services are running
echo.
echo  [INFO] Checking services...

netstat -an | findstr :80 >nul 2>&1
if %errorlevel% == 0 (
    echo  [OK] Apache is running
) else (
    echo  [ERROR] Apache not running - please start it in XAMPP
    pause
    exit /b 1
)

netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% == 0 (
    echo  [OK] MySQL is running
) else (
    echo  [ERROR] MySQL not running - please start it in XAMPP
    pause
    exit /b 1
)

echo.
echo  [SUCCESS] All services running!
echo.

REM Setup WordPress if needed
if not exist "C:\xampp\htdocs\ballery\wp-config.php" (
    echo  [INFO] Setting up WordPress...
    
    REM Create directory
    if not exist "C:\xampp\htdocs\ballery" mkdir "C:\xampp\htdocs\ballery"
    
    REM Copy files
    echo  [INFO] Copying theme files...
    xcopy /E /I /Y "%~dp0*" "C:\xampp\htdocs\ballery\" >nul 2>&1
    
    REM Open setup page
    echo  [INFO] Opening WordPress setup...
    start http://localhost/ballery/wordpress-setup.php
    
    echo.
    echo  Complete the WordPress setup, then press any key...
    pause >nul
)

REM Run database setup
echo.
echo  [INFO] Setting up booking system database...
start http://localhost/ballery/setup-database.php

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Open verification
echo  [INFO] Opening setup verification...
start http://localhost/ballery/verify-setup.php

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Open the website
echo.
echo  ================================================================
echo                        SETUP COMPLETE!
echo  ================================================================
echo.
echo  Your BALLERY.IN website is now running locally!
echo.
echo  Access URLs:
echo  - Website:        http://localhost/ballery
echo  - WordPress Admin: http://localhost/ballery/wp-admin
echo  - Database:       http://localhost/phpmyadmin
echo.
echo  Default Login:
echo  - Username: admin
echo  - Password: BalleryAdmin2024!
echo.
echo  Opening your website...

start http://localhost/ballery

echo.
echo  [SUCCESS] BALLERY.IN is now running locally!
echo.
echo  Press any key to exit...
pause >nul
